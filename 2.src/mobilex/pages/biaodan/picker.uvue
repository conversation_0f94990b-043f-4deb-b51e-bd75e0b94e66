<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
		<navigation-bar :background-color="xThemeConfigNavBgColor" :front-color="xThemeConfigNavFontColor"></navigation-bar>
	</page-meta>
	<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">选择器 Picke</x-text>
			<x-text color="#999999" class="text-size-b">
				这是xPickerView封装的弹出式，详细可见xPickerView文档
			</x-text>
		</x-sheet>
		<x-sheet>

			<x-picker @change="change" v-model="selecteds" v-model:model-str="(str as string)" :list="list">
				<x-button :block="true">打开选项</x-button>
			</x-picker>

			<x-sheet :margin="['0','24']" color="#f5f5f5" dark-color="#333">
				<x-text color="#999999">选中的值：{{selecteds.join(',')}}</x-text>
				<x-text color="#999999">选中的值回显文本：{{str}}</x-text>
			</x-sheet>
			<x-button :block="true" @click="fuzhi">赋值</x-button>
		</x-sheet>
		
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">customWrapStyle定义外观</x-text>
			
		</x-sheet>
		<x-sheet>
			<x-picker customWrapStyle="margin:16px;width:auto;border-radius:16px;" @change="change" v-model="selecteds" v-model:model-str="(str as string)" :list="list">
				<x-button :block="true">打开选项</x-button>
			</x-picker>
		</x-sheet>
		
		<view style="height: 50px;"></view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script lang="ts">
	import { PICKER_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"
	const items = [
		{
			title: '江西',
			id: "9-1",
			children: [
				{
					title: '南昌',
					id: "132",
					children: [
						{ title: '青山湖区', id: "1-2" } as PICKER_ITEM_INFO,
						{ title: '高新区', id: "1-3", disabled: true } as PICKER_ITEM_INFO,
						{ title: '红谷滩区', id: "1-4" } as PICKER_ITEM_INFO,
					] as PICKER_ITEM_INFO[],

				} as PICKER_ITEM_INFO,
				{

					title: '九江', id: "222",
					children: [
						{ title: '2青山湖区', id: "1-32" } as PICKER_ITEM_INFO,
						{ title: '2高新区', id: "1-33" } as PICKER_ITEM_INFO,
						{ title: '3红谷滩区', id: "1-34" } as PICKER_ITEM_INFO,
					] as PICKER_ITEM_INFO[],
				} as PICKER_ITEM_INFO,
			] as PICKER_ITEM_INFO[],
		} as PICKER_ITEM_INFO,
		{
			title: '安徽',
			id: "10-13",
			children: [
				{
					title: '5南昌',
					id: "3",
					children: [
						{ title: '5青山湖区', id: "1-52" } as PICKER_ITEM_INFO,
						{ title: '5高新区', id: "1-53" } as PICKER_ITEM_INFO,
						{ title: '5红谷滩区', id: "1-54" } as PICKER_ITEM_INFO,
					] as PICKER_ITEM_INFO[],

				} as PICKER_ITEM_INFO,
				{
					title: '5南昌',
					id: "36",
					children: [
						{ title: '6青山湖区', id: "61-52" } as PICKER_ITEM_INFO,
						{ title: '6高新区', id: "61-53" } as PICKER_ITEM_INFO,
						{ title: '6红谷滩区', id: "61-54" } as PICKER_ITEM_INFO,
					] as PICKER_ITEM_INFO[],

				} as PICKER_ITEM_INFO,
			] as PICKER_ITEM_INFO[],
		} as PICKER_ITEM_INFO,

	] as PICKER_ITEM_INFO[];
	export default {
		data() {

			return {
				list: items,
				// '10-1', '1', '1-3'
				selecteds: [] as string[],
				str: ""

			};
		},
		methods: {
			change(ids : string[]) {
				console.log(ids)
			},
			fuzhi() {
				this.selecteds = ['10-13', '36', '61-53']

			}
		},
	}
</script>

<style lang="scss">

</style>