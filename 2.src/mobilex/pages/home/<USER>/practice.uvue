<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
			<navigation-bar :background-color="xThemeConfigNavBgColor"
				:front-color="xThemeConfigNavFontColor"></navigation-bar>
		</page-meta>
		<!-- 小程序状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<!-- #ifdef H5 -->
		<!-- H5状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<!-- 切换科目 -->
		<x-sheet>
			<view class="flex flex-row flex-row-center-between">
				<x-text font-size="18" class="text-weight-b">当前科目</x-text>
				<x-button round="10" color="primary" size="small">切换科目</x-button>
			</view>
			<x-text font-size="24" class="text-weight-b mt-16">行政职业能力测验</x-text>
		</x-sheet>

		<!-- 练题进度 -->
		<x-sheet>
			<view class="flex flex-row flex-row-center-between mb-16">
				<x-text font-size="18" class="text-weight-b">练题进度</x-text>
				<x-text color="#999999">已完成 <text class="text-primary">120</text>/1000 题</x-text>
			</view>
			<x-progress :model-value="12" :show-label="true" color="primary"></x-progress>
		</x-sheet>

		<!-- 宫格菜单 -->
		<x-sheet>
			<x-text font-size="18" class="text-weight-b mb-16">学习模式</x-text>
			<x-grid :col="3" icon-color="primary" dark-icon-color="primary">
				<x-grid-item icon="order-play-fill" text="顺序练题"></x-grid-item>
				<x-grid-item icon="shuffle-fill" text="随机练题"></x-grid-item>
				<x-grid-item icon="file-paper-2-fill" text="历年真题"></x-grid-item>
				<x-grid-item icon="error-warning-fill" text="我的错题"></x-grid-item>
				<x-grid-item icon="star-fill" text="我的收藏"></x-grid-item>
				<x-grid-item icon="book-marked-line" text="我的笔记"></x-grid-item>
			</x-grid>
		</x-sheet>

		<!-- 每日时政滚动展示 -->
		<x-sheet>
			<view class="flex flex-row flex-row-center-start mb-16">
				<x-text font-size="18" class="text-weight-b mr-16">每日时政</x-text>
				<x-badge label="NEW" bg-color="error"></x-badge>
			</view>
			<x-swiper height="120" :vertical="true" :autoPlay="true" :interval="3000">
				<x-swiper-item v-for="(item, index) in newsItems" :key="index" :order="index">
					<view class="news-item">
						<x-text font-size="16">{{ item.title }}</x-text>
						<x-text font-size="14" color="#999999" class="mt-8">{{ item.date }}</x-text>
					</view>
				</x-swiper-item>
			</x-swiper>
		</x-sheet>

		<!-- 知识点列表 -->
		<x-sheet>
			<x-text font-size="18" class="text-weight-b mb-16">知识点列表</x-text>
			<x-tree :parentSelectedFullChildren="false" v-model="selectedKnowledge" v-model:folder-id="openedKnowledge"
				:list="knowledgeList"></x-tree>
		</x-sheet>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script lang="uts" setup>
	import { ref, onMounted } from 'vue'
	import { NewsItem, KnowledgeItem } from '../../../types/home.uts'

	// 状态栏高度
	const statusBarHeight = ref<number>(0)

	// 每日时政数据
	const newsItems = ref<NewsItem[]>([
		{
			title: "国务院印发《关于加强基层治理体系和治理能力现代化建设的意见》",
			date: "2023-12-15"
		},
		{
			title: "中央经济工作会议在北京举行，部署2024年经济工作",
			date: "2023-12-12"
		},
		{
			title: "全国政协十四届常委会第四次会议在京开幕",
			date: "2023-12-10"
		},
		{
			title: "中共中央政治局召开会议，分析研究2024年经济工作",
			date: "2023-12-08"
		},
		{
			title: "国家主席习近平签署主席令，任命李尚福为国防部部长",
			date: "2023-12-05"
		}
	])

	// 知识点树状结构数据
	const selectedKnowledge = ref<string[]>([])
	const openedKnowledge = ref<string[]>([])
	const knowledgeList = ref<UTSJSONObject[]>([
		{
			id: "1",
			text: "言语理解与表达",
			children: [
				{
					id: "1-1",
					text: "片段阅读 (45/100题)",
					children: [
						{
							id: "1-1-1",
							text: "中心理解题 (15/30题)"
						},
						{
							id: "1-1-2",
							text: "细节判断题 (20/40题)"
						},
						{
							id: "1-1-3",
							text: "推断下文题 (10/30题)"
						}
					]
				},
				{
					id: "1-2",
					text: "语句表达 (30/80题)",
					children: [
						{
							id: "1-2-1",
							text: "语句填空 (15/40题)"
						},
						{
							id: "1-2-2",
							text: "语句排序 (15/40题)"
						}
					]
				}
			]
		},
		{
			id: "2",
			text: "数量关系",
			children: [
				{
					id: "2-1",
					text: "数字推理 (10/50题)"
				},
				{
					id: "2-2",
					text: "数学运算 (5/50题)"
				}
			]
		},
		{
			id: "3",
			text: "判断推理",
			children: [
				{
					id: "3-1",
					text: "图形推理 (20/60题)"
				},
				{
					id: "3-2",
					text: "定义判断 (15/40题)"
				},
				{
					id: "3-3",
					text: "类比推理 (10/30题)"
				}
			]
		}
	])

	// 页面加载时执行
	onMounted(() => {
		// 获取状态栏高度
		const sysInfo = uni.getSystemInfoSync()
		// 根据平台设置不同的状态栏高度
		// #ifdef APP
		statusBarHeight.value = sysInfo.statusBarHeight | 20
		// #endif

		// #ifdef MP-WEIXIN
		statusBarHeight.value = sysInfo.statusBarHeight | 0
		// #endif

		// #ifdef H5
		statusBarHeight.value = 20 // H5平台固定高度
		// #endif

		// 初始化时打开第一个知识点
		openedKnowledge.value = ['1']
	})
</script>

<style lang="scss">
	.status-bar {
		background-color: var(--x-bg-color, #f5f5f5);
		width: 100%;
		transition: height 0.3s;
		position: relative;
		z-index: 100;
	}

	.news-item {
		padding: 16rpx;
		border-radius: 8rpx;
		background-color: rgba(0, 115, 255, 0.05);
	}
</style>