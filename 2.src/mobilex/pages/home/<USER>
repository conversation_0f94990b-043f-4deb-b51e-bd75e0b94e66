<template>
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:#f5f5f5`">
		<navigation-bar background-color="#f5f5f5" front-color="#000000"></navigation-bar>
	</page-meta>
	<!-- #endif -->

	<swiper :duration="0" :circular="false" :current="current" @change="swiperChange" class="flex-1">
		<swiper-item>
			<practice-uvue v-if="isReady"></practice-uvue>
		</swiper-item>
		<swiper-item>
			<politics-uvue v-if="isReady&&(pagesStatus.get(1)==true)"></politics-uvue>
		</swiper-item>
		<swiper-item>
			<mine-uvue v-if="isReady&&(pagesStatus.get(2)==true)"></mine-uvue>
		</swiper-item>
	</swiper>

	<x-tabbar v-if="isReady" :outIndex="-1" @click="tabsItemClick" :is-canvas-render="false"
		v-model:autoTabbarHeight="autoTabbarHeight" color="#808080">
	</x-tabbar>
	<view :style="{height:autoTabbarHeight+'px'}"></view>
</template>

<script lang="uts" setup>
	import { ref, onMounted, onBeforeMount } from 'vue'
	import { xStore } from "@/uni_modules/tmx-ui/index.uts"
	import { TABBAR_ITEM_INFO, TABBAR_ITEM } from "@/uni_modules/tmx-ui/interface.uts"
	import practiceUvue from "./components/practice.uvue"
	import politicsUvue from "./components/politics.uvue"
	import mineUvue from "./components/mine.uvue"

	// 响应式数据
	const isReady = ref<boolean>(false)
	const current = ref<number>(0)
	const autoTabbarHeight = ref<number>(0)

	// tabbar配置
	const tabsList = ref<TABBAR_ITEM_INFO[]>([
		{ title: "练题", icon: "edit-2-line", selectedIcon: "edit-2-fill" } as TABBAR_ITEM_INFO,
		{ title: "时政", icon: "fire-line", selectedIcon: "fire-fill" } as TABBAR_ITEM_INFO,
		{ title: "我的", icon: "user-line", selectedIcon: "user-fill" } as TABBAR_ITEM_INFO
	])

	//管理页面显示状态，如果已经显示过就直接显示，没有显示过隐藏不显示，以加快渲染速度。
	const pagesStatus = new Map<number, boolean>([
		[0, true],
		[1, false],
		[2, false],
	])

	// 方法
	function swiperChange(evt : UniSwiperChangeEvent) {
		console.log(evt);
		pagesStatus.set(evt.detail.current, true)
		current.value = evt.detail.current
		xStore.xTabbarConfig.tabbarActiveIndex = current.value
	}

	function tabsItemClick(index : number) {
		pagesStatus.set(index, true)
		current.value = index
	}

	// 生命周期
	onBeforeMount(() => {
		xStore.xTabbarConfig.list = tabsList.value;
	})

	onMounted(() => {
		isReady.value = true
	})
</script>

<style lang="scss">
	/* #ifdef MP */
	page,
	body {
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	/* #endif */
</style>