<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<!-- #ifdef H5 -->
		<!-- H5状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<view class="politics-container">
			<!-- 头部搜索区域 -->
			<x-sheet>
				<view class="header-section">
					<x-text font-size="24" class="text-weight-b">时政要闻</x-text>
					<x-search placeholder="搜索时政新闻" v-model="searchKeyword" @search="onSearch"
						@clear="onClear"></x-search>
				</view>
			</x-sheet>

			<!-- 分类标签 -->
			<x-sheet>
				<x-tabs v-model="currentCategory" :list="categoryList"></x-tabs>
			</x-sheet>

			<!-- 新闻列表 -->
			<x-sheet :padding="['0']">
				<view class="news-list">
					<view class="news-item" v-for="(item, index) in filteredNewsList" :key="index"
						@click="goToNewsDetail(item)">
						<view class="news-content">
							<view class="news-header">
								<x-text font-size="16" class="text-weight-b news-title">{{ item.title }}</x-text>
								<x-badge v-if="item.isHot" label="热点" bg-color="error" class="hot-badge"></x-badge>
							</view>
							<x-text font-size="14" color="#666666" class="news-summary">{{ item.summary }}</x-text>
							<view class="news-meta">
								<x-text font-size="12" color="#999999">{{ item.source }}</x-text>
								<x-text font-size="12" color="#999999">{{ formatDate(item.publishTime) }}</x-text>
								<view class="news-actions">
									<x-icon name="eye-line" size="14" color="#999999"></x-icon>
									<x-text font-size="12" color="#999999" class="ml-4">{{ item.readCount }}</x-text>
								</view>
							</view>
						</view>
						<view class="news-image" v-if="item.imageUrl">
							<x-image :src="item.imageUrl" width="80" height="60" round="8" mode="aspectFill"></x-image>
						</view>
					</view>
				</view>
			</x-sheet>

			<!-- 加载更多 -->
			<x-sheet v-if="hasMore">
				<view class="load-more" @click="loadMore">
					<x-loading v-if="loading" size="small"></x-loading>
					<x-text v-else color="primary">点击加载更多</x-text>
				</view>
			</x-sheet>
		</view>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script lang="uts" setup>
	import { ref, computed, onMounted } from 'vue'
	import { News } from '@/types/home.uts'
	import { TABS_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"
	// 响应式数据
	const statusBarHeight = ref<number>(0)
	const searchKeyword = ref<string>('')
	const currentCategory = ref<string>('all')
	const loading = ref<boolean>(false)
	const hasMore = ref<boolean>(true)

	// 分类数据
	const categoryList = ref<TABS_ITEM_INFO[]>([
		{ title: '全部', id: 'all' },
		{ title: '国内', id: 'domestic' },
		{ title: '国际', id: 'international' },
		{ title: '经济', id: 'economy' },
		{ title: '社会', id: 'society' },
		{ title: '科技', id: 'technology' }
	])

	// 新闻数据
	const newsList = ref<News[]>([
		{
			id: '1',
			title: '国务院印发《关于加强基层治理体系和治理能力现代化建设的意见》',
			summary: '为深入贯彻党的二十大精神，加强基层治理体系和治理能力现代化建设，国务院印发相关意见...',
			source: '新华社',
			publishTime: Date.now() - 3600000,
			readCount: 1250,
			category: 'domestic',
			isHot: true,
			imageUrl: 'https://picsum.photos/200/150?random=1'
		},
		{
			id: '2',
			title: '中央经济工作会议在北京举行，部署2024年经济工作',
			summary: '中央经济工作会议12月11日至12日在北京举行，会议总结2023年经济工作，分析当前经济形势...',
			source: '人民日报',
			publishTime: Date.now() - 7200000,
			readCount: 980,
			category: 'economy',
			isHot: true,
			imageUrl: 'https://picsum.photos/200/150?random=2'
		},
		{
			id: '3',
			title: '全国政协十四届常委会第四次会议在京开幕',
			summary: '全国政协十四届常委会第四次会议10日在京开幕，会议将围绕"推进中国式现代化"进行专题议政...',
			source: '央视新闻',
			publishTime: Date.now() - 10800000,
			readCount: 756,
			category: 'domestic',
			isHot: false,
			imageUrl: 'https://picsum.photos/200/150?random=3'
		},
		{
			id: '4',
			title: '我国成功发射高分十四号02星',
			summary: '12月9日11时26分，我国在西昌卫星发射中心使用长征三号乙运载火箭，成功将高分十四号02星发射升空...',
			source: '科技日报',
			publishTime: Date.now() - 14400000,
			readCount: 432,
			category: 'technology',
			isHot: false,
			imageUrl: 'https://picsum.photos/200/150?random=4'
		},
		{
			id: '5',
			title: '联合国气候变化大会达成重要共识',
			summary: '在迪拜举行的联合国气候变化大会经过激烈讨论，各国代表就应对气候变化问题达成重要共识...',
			source: '环球时报',
			publishTime: Date.now() - 18000000,
			readCount: 623,
			category: 'international',
			isHot: false,
			imageUrl: 'https://picsum.photos/200/150?random=5'
		},
		{
			id: '6',
			title: '教育部发布2024年高校毕业生就业创业工作通知',
			summary: '教育部近日发布通知，要求各地各高校深入学习贯彻党的二十大精神，全力促进高校毕业生就业创业...',
			source: '中国教育报',
			publishTime: Date.now() - 21600000,
			readCount: 891,
			category: 'society',
			isHot: false,
			imageUrl: 'https://picsum.photos/200/150?random=6'
		}
	])

	// 过滤后的新闻列表
	const filteredNewsList = computed<News[]>(() => {
		let filtered = newsList.value
		filtered = filtered.filter(item => item.category === currentCategory.value)

		// 按搜索关键词过滤
		if (searchKeyword.value.trim() !== '') {
			const keyword = searchKeyword.value.toLowerCase()
			filtered = filtered.filter(item =>
				item.title.toLowerCase().includes(keyword) ||
				item.summary.toLowerCase().includes(keyword)
			)
		}

		return filtered
	})

	// 方法
	const formatDate = (timestamp : number) : string => {
		const now = Date.now()
		const diff = now - timestamp
		const minutes = Math.floor(diff / 60000)
		const hours = Math.floor(diff / 3600000)
		const days = Math.floor(diff / 86400000)

		if (minutes < 60) {
			return `${minutes}分钟前`
		} else if (hours < 24) {
			return `${hours}小时前`
		} else {
			return `${days}天前`
		}
	}

	const onSearch = (keyword : string) : void => {
		searchKeyword.value = keyword
		console.log('搜索关键词:', keyword)
	}

	const onClear = () : void => {
		searchKeyword.value = ''
	}

	const goToNewsDetail = (item : News) : void => {
		console.log('查看新闻详情:', item.title)
		uni.navigateTo({
			url: `/pages/news/detail?id=${item.id}`
		})
	}

	const loadMore = () : void => {
		if (loading.value) return

		loading.value = true
		console.log('加载更多新闻...')

		// 模拟加载更多数据
		setTimeout(() => {
			loading.value = false
			// 这里可以添加更多新闻数据
			hasMore.value = false // 模拟没有更多数据
		}, 2000)
	}

	// 生命周期
	onMounted(() => {
		// 获取状态栏高度
		const sysInfo = uni.getSystemInfoSync()
		// #ifdef APP
		statusBarHeight.value = sysInfo.statusBarHeight | 20
		// #endif
		// #ifdef MP-WEIXIN
		statusBarHeight.value = sysInfo.statusBarHeight | 0
		// #endif
		// #ifdef H5
		statusBarHeight.value = 20
		// #endif
	})
</script>

<style lang="scss">
	.status-bar {
		background-color: var(--x-bg-color, #f5f5f5);
		width: 100%;
		transition: height 0.3s;
		position: relative;
		z-index: 100;
	}

	.politics-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header-section {
		margin-bottom: 16px;
	}

	.news-list {
		background-color: #ffffff;
	}

	.news-item {
		display: flex;
		flex-direction: row;
		padding: 16px;
		border-bottom: 1px solid #f5f5f5;
		background-color: #ffffff;
	}

	.news-item:last-child {
		border-bottom: none;
	}

	.news-content {
		flex: 1;
		margin-right: 12px;
	}

	.news-header {
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		margin-bottom: 8px;
	}

	.news-title {
		flex: 1;
		line-height: 1.4;
		margin-right: 8px;
	}

	.hot-badge {
		flex-shrink: 0;
	}

	.news-summary {
		line-height: 1.5;
		margin-bottom: 12px;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.news-meta {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.news-actions {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.news-image {
		flex-shrink: 0;
	}

	.load-more {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding: 20px;
		background-color: #ffffff;
	}

	/* 深色模式适配 */
	/* #ifdef APP */
	@media (prefers-color-scheme: dark) {
		.politics-container {
			background-color: #1a1a1a;
		}

		.news-list {
			background-color: #2a2a2a;
		}

		.news-item {
			background-color: #2a2a2a;
			border-bottom-color: #3a3a3a;
		}

		.load-more {
			background-color: #2a2a2a;
		}
	}

	/* #endif */
</style>