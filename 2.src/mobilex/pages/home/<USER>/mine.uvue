<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<!-- #ifdef H5 -->
		<!-- H5状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<!-- #endif -->

		<view class="mine-container">
			<!-- 用户信息 -->
			<x-sheet :padding="['20', '16', '20', '16']">
				<view class="user-section">
					<view class="user-info">
						<x-sheet class="avatar-container" width="60" height="60" :round="['50%']" :margin="['0']"
							:padding="['0']">
							<x-image src="https://picsum.photos/100/100?random=user" width="60" height="60" round="30"
								mode="aspectFill"></x-image>
						</x-sheet>
						<view class="user-details">
							<x-text font-size="18"
								class="text-weight-b">{{ userInfo.nickname != null && userInfo.nickname != '' ? userInfo.nickname : '点击登录' }}</x-text>
							<x-text font-size="14"
								color="#666666">{{ userInfo.phone != null && userInfo.phone != '' ? userInfo.phone : '获取更多学习权益' }}</x-text>
						</view>
					</view>
					<x-icon name="arrow-right-s-line" size="20" color="#999999"></x-icon>
				</view>
			</x-sheet>

			<!-- VIP卡片 -->
			<x-sheet :padding="['0']" v-if="!userInfo.isVip">
				<view class="vip-card">
					<view class="vip-content">
						<x-text font-size="16" class="text-weight-b" color="#ffffff">升级会员</x-text>
						<x-text font-size="14" color="#ffffff" style="opacity: 0.9;">解锁全部题库，享受专属服务</x-text>
					</view>
					<x-button size="small" bg-color="#ffffff" color="primary" round="20">立即升级</x-button>
				</view>
			</x-sheet>

			<!-- 统计网格 -->
			<x-sheet :padding="['16', '8', '16', '8']">
				<x-grid :col="4" :border="false">
					<x-grid-item v-for="(item, key) in statsData" :key="key">
						<view class="stat-item">
							<x-text font-size="20" class="text-weight-b" color="primary">{{ item.value }}</x-text>
							<x-text font-size="12" color="#666666">{{ item.label }}</x-text>
						</view>
					</x-grid-item>
				</x-grid>
			</x-sheet>

			<!-- 学习计划 -->
			<x-sheet :padding="['20', '16', '16', '16']">
				<view class="plan-section">
					<view class="plan-header">
						<x-text font-size="16" class="text-weight-b">学习计划</x-text>
						<x-text font-size="14" color="primary">查看全部</x-text>
					</view>
					<view class="plan-item" v-for="(item, key) in planList" :key="key">
						<view class="plan-info">
							<x-text font-size="14" class="text-weight-b">{{ item.title }}</x-text>
							<x-text font-size="12" color="#666666">{{ item.description }}</x-text>
						</view>
						<view class="plan-progress">
							<x-progress :percent="item.progress" height="6" bg-color="#f5f5f5"
								active-color="primary"></x-progress>
							<x-text font-size="12" color="#666666" class="mt-4">{{ item.progress }}%</x-text>
						</view>
					</view>
				</view>
			</x-sheet>

			<!-- 功能菜单 -->
			<x-sheet :padding="['16', '8', '16', '8']">
				<x-grid :col="3" item-height="90" text-color="#333333">
					<x-grid-item v-for="(item, index) in menuList" :key="index" :icon="item.icon" :text="item.title"
						:iconColor="item.color" @click="handleMenuClick(item)">
						<x-badge v-if="item.badge > 0" :count="item.badge" bg-color="danger">
							<x-icon :name="item.icon" :color="item.color" font-size="24" class="mb-5"></x-icon>
							<x-text font-size="12">{{ item.title }}</x-text>
						</x-badge>
					</x-grid-item>
				</x-grid>
			</x-sheet>

			<!-- 学习统计和今日计划切换 -->
			<x-sheet :padding="['20', '16', '16', '16']">
				<x-tabs item-width="50%" v-model="currentTab" :list="tabList"></x-tabs>

				<view class=" chart-section" v-if="currentTab === 'chart'">
					<x-echart ref="studyChart" :opts="studyChartOpts" @init="initStudyChart"
						style="height: 320px;"></x-echart>
				</view>

				<!-- 今日计划 -->
				<view class="plan-today" v-else>
					<view class="today-item" v-for="(item, key) in todayPlan" :key="key">
						<view class="today-left">
							<x-icon :name="item.completed ? 'checkbox-circle-fill' : 'checkbox-blank-circle-line'"
								:color="item.completed ? 'success' : '#cccccc'" size="20"></x-icon>
							<view class="today-content">
								<x-text font-size="14"
									:class="item.completed ? 'text-line-through' : ''">{{ item.title }}</x-text>
								<x-text font-size="12" color="#666666">{{ item.time }}</x-text>
							</view>
						</view>
						<x-text font-size="12" :color="item.completed ? 'success' : 'warning'">
							{{ item.completed ? '已完成' : '进行中' }}
						</x-text>
					</view>
				</view>
			</x-sheet>

			<!-- 底部信息 -->
			<x-sheet :padding="['20', '16', '20', '16']">
				<view class="footer-info">
					<x-text font-size="12" color="#999999" text-align="center">版本号：v1.0.0</x-text>
					<x-text font-size="12" color="#999999" text-align="center">© 2024 学习助手</x-text>
				</view>
			</x-sheet>
		</view>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script lang="uts" setup>
	import { ref, reactive, onMounted } from 'vue'
	import { GridItem, TodayPlan, StudyPlan } from '@/types/home.uts'
	import { UserInfo, SelectItem } from '@/types/index.uts'
	import { TABS_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"
	// 响应式数据
	const statusBarHeight = ref<number>(0)
	const currentTab = ref<string>('chart')
	const studyChart = ref(null)

	// 用户信息
	const userInfo = reactive<UserInfo>({
		nickname: '学习者',
		phone: '138****8888',
		isVip: false
	})

	// 统计数据
	const statsData = ref<SelectItem[]>([
		{ label: '练题记录', value: '1,234' },
		{ label: '学习天数', value: '89' },
		{ label: '正确率', value: '85%' },
		{ label: '学习时长', value: '156h' }
	])

	// 学习计划
	const planList = ref<StudyPlan[]>([
		{
			title: '行政法基础',
			description: '掌握行政法基本概念和原理',
			progress: 75
		},
		{
			title: '民法典解读',
			description: '深入理解民法典重点条文',
			progress: 45
		},
		{
			title: '刑法案例分析',
			description: '通过案例学习刑法适用',
			progress: 60
		}
	])

	// 功能菜单
	const menuList = ref<GridItem[]>([
		{
			icon: 'file-list-3-line',
			title: '练题记录',
			color: '#3b82f6',
			badge: 0
		},
		{
			icon: 'calendar-check-line',
			title: '学习计划',
			color: '#10b981',
			badge: 3
		},
		{
			icon: 'trophy-line',
			title: '成就徽章',
			color: '#f59e0b',
			badge: 0
		},
		{
			icon: 'bookmark-line',
			title: '收藏夹',
			color: '#ef4444',
			badge: 0
		},
		{
			icon: 'download-line',
			title: '离线下载',
			color: '#8b5cf6',
			badge: 0
		},
		{
			icon: 'settings-3-line',
			title: '设置',
			color: '#6b7280',
			badge: 0
		}
	])

	// Tab列表
	const tabList = ref<TABS_ITEM_INFO[]>([
		{ title: '学习统计', id: 'chart' },
		{ title: '今日计划', id: 'plan' }
	])

	// 今日计划
	const todayPlan = ref<TodayPlan[]>([
		{
			title: '完成行政法练习题 20道',
			time: '09:00 - 10:00',
			completed: true
		},
		{
			title: '阅读民法典第一章',
			time: '14:00 - 15:30',
			completed: true
		},
		{
			title: '观看刑法视频课程',
			time: '19:00 - 20:30',
			completed: false
		},
		{
			title: '复习今日错题',
			time: '21:00 - 21:30',
			completed: false
		}
	])

	// 图表配置
	const studyChartOpts = ref('')

	// 日历标记
	const calendarMarks = ref([
		{ date: '2024-01-15', color: 'success', text: '已学习' },
		{ date: '2024-01-16', color: 'warning', text: '部分完成' },
		{ date: '2024-01-17', color: 'success', text: '已学习' }
	])

	const initStudyChart = () : void => {
		console.log('初始化学习统计图表')

		// 创建图表配置
		const option = {
			title: {
				text: '学习统计',
				left: 'center',
				textStyle: {
					fontSize: 16,
					fontWeight: 'bold'
				}
			},
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'cross'
				}
			},
			legend: {
				data: ['学习时长', '完成题目数'],
				top: 30
			},
			grid: {
				left: '3%',
				right: '4%',
				bottom: '3%',
				containLabel: true
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
				axisLabel: {
					fontSize: 12
				}
			},
			yAxis: [
				{
					type: 'value',
					name: '时长(小时)',
					position: 'left',
					axisLabel: {
						formatter: '{value}h',
						fontSize: 12
					}
				},
				{
					type: 'value',
					name: '题目数',
					position: 'right',
					axisLabel: {
						formatter: '{value}题',
						fontSize: 12
					}
				}
			],
			series: [
				{
					name: '学习时长',
					type: 'bar',
					yAxisIndex: 0,
					data: [2.5, 3.2, 1.8, 4.1, 2.9, 3.5, 2.2],
					itemStyle: {
						color: '#3b82f6'
					}
				},
				{
					name: '完成题目数',
					type: 'line',
					yAxisIndex: 1,
					data: [45, 62, 28, 78, 52, 68, 38],
					itemStyle: {
						color: '#10b981'
					},
					lineStyle: {
						color: '#10b981'
					}
				}
			]
		}

		studyChartOpts.value = JSON.stringify(option);
	}
	watch(() : string => currentTab.value, (value : string, prevValue : string) => {
		if (value === 'chart') {
			// 切换到学习统计时重新初始化图表
			initStudyChart()
		}
	})


	const handleMenuClick = (item : GridItem) : void => {
		console.log('点击菜单:', item.title)
	}


	// 生命周期
	onMounted(() => {
		// 获取状态栏高度
		const sysInfo = uni.getSystemInfoSync()
		// #ifdef APP
		statusBarHeight.value = sysInfo.statusBarHeight | 20
		// #endif
		// #ifdef MP-WEIXIN
		statusBarHeight.value = sysInfo.statusBarHeight | 0
		// #endif
		// #ifdef H5
		statusBarHeight.value = 20
		// #endif

		// 初始化图表
		setTimeout(() => {
			initStudyChart()
		}, 500)
	})
</script>

<style lang="scss">
	.status-bar {
		background-color: var(--x-bg-color, #f5f5f5);
		width: 100%;
		transition: height 0.3s;
		position: relative;
		z-index: 100;
	}

	.mine-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.user-section {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0;
	}

	.user-info {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.user-details {
		margin-left: 12px;
	}

	.avatar-container {
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.vip-card {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 12px;
		padding: 16px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.vip-content {
		flex: 1;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20px 8px;
	}

	.plan-section {
		padding: 0;
	}

	.plan-header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16px;
	}

	.plan-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16px;
		padding: 16px;
		border-radius: 8px;
		background-color: #fafafa;
		border: 1px solid #f0f0f0;
	}

	.plan-item:last-child {
		margin-bottom: 0;
	}

	.plan-info {
		flex: 1;
		margin-right: 16px;
	}

	.plan-progress {
		width: 100px;
	}



	.custom-tabs {
		margin-bottom: 20px;
	}

	.chart-section {
		margin-top: 0;
		padding: 16px;
		border-radius: 12px;
		background-color: #fafafa;
	}

	.plan-today {
		margin-top: 0;
		padding: 16px;
		border-radius: 12px;
		background-color: #fafafa;
	}

	.today-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 16px;
		margin-bottom: 12px;
		border-radius: 8px;
		background-color: #ffffff;
		border: 1px solid #f0f0f0;
	}

	.today-item:last-child {
		margin-bottom: 0;
	}

	.today-left {
		display: flex;
		flex-direction: row;
		align-items: center;
		flex: 1;
	}

	.today-content {
		margin-left: 12px;
		flex: 1;
	}

	.footer-info {
		padding: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8px;
	}

	.calendar-popup {
		padding: 16px;
	}

	.calendar-header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16px;
	}

	/* 深色模式适配 */
	/* #ifdef APP */
	@media (prefers-color-scheme: dark) {
		.mine-container {
			background-color: #1a1a1a;
		}

		.calendar-popup {
			background-color: #2a2a2a;
		}
	}

	/* #endif */
</style>