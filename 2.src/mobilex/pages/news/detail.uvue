<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- 新闻内容 -->
		<x-sheet v-if="newsDetail">
			<!-- 新闻标题 -->
			<x-text font-size="20" class="text-weight-b news-title">{{ newsDetail.title }}</x-text>
			
			<!-- 新闻信息 -->
			<view class="news-meta">
				<view class="meta-left">
					<x-text font-size="12" color="#666666">{{ newsDetail.source }}</x-text>
					<x-text font-size="12" color="#666666" class="ml-16">{{ newsDetail.author }}</x-text>
				</view>
				<x-text font-size="12" color="#999999">{{ formatDate(newsDetail.publishTime) }}</x-text>
			</view>
			
			<!-- 标签 -->
			<view class="news-tags" v-if="newsDetail.tags && newsDetail.tags.length > 0">
				<x-tag v-for="tag in newsDetail.tags" :key="tag" size="small" color="primary" class="mr-8">{{ tag }}</x-tag>
			</view>
			
			<!-- 新闻图片 -->
			<view class="news-image" v-if="newsDetail.imageUrl">
				<x-image :src="newsDetail.imageUrl" width="100%" height="200" round="8" mode="aspectFill"></x-image>
			</view>
			
			<!-- 新闻正文 -->
			<view class="news-content">
				<x-text font-size="16" class="news-text">{{ newsDetail.content }}</x-text>
			</view>
			
			<!-- 操作按钮 -->
			<view class="news-actions">
				<x-button @click="toggleCollect" 
					:color="newsDetail.isCollected ? 'warn' : 'grey'" 
					size="small" width="80" height="32">
					<x-icon :name="newsDetail.isCollected ? 'star-fill' : 'star-line'" size="14"></x-icon>
					{{ newsDetail.isCollected ? '已收藏' : '收藏' }}
				</x-button>
				<x-button @click="shareNews" color="primary" size="small" width="80" height="32" class="ml-8">
					<x-icon name="share-line" size="14"></x-icon>
					分享
				</x-button>
			</view>
		</x-sheet>

		<!-- 评论区域 -->
		<x-sheet>
			<view class="comment-section">
				<x-text font-size="16" class="text-weight-b mb-16">评论 ({{ commentsList.length }})</x-text>
				
				<!-- 评论输入框 -->
				<view class="comment-input">
					<x-input v-model="commentContent" 
						placeholder="写下你的评论..." 
						:maxlength="200"
						class="comment-field">
					</x-input>
					<x-button @click="submitComment" 
						color="primary" 
						size="small" 
						width="60" 
						height="32"
						:disabled="commentContent.trim() === ''">
						发布
					</x-button>
				</view>
				
				<!-- 评论列表 -->
				<view class="comments-list" v-if="commentsList.length > 0">
					<view class="comment-item" v-for="comment in commentsList" :key="comment.id">
						<view class="comment-header">
							<view class="user-info">
								<x-image :src="comment.userAvatar" width="32" height="32" round="16" mode="aspectFill"></x-image>
								<view class="user-details">
									<x-text font-size="14" class="text-weight-b">{{ comment.userName }}</x-text>
									<x-text font-size="12" color="#999999">{{ formatDate(comment.createTime) }}</x-text>
								</view>
							</view>
							<x-button @click="likeComment(comment)" 
								:color="comment.isLiked ? 'error' : 'grey'" 
								size="small" 
								width="60" 
								height="24">
								<x-icon :name="comment.isLiked ? 'heart-fill' : 'heart-line'" size="12"></x-icon>
								{{ comment.likeCount }}
							</x-button>
						</view>
						
						<view class="comment-content">
							<x-text font-size="14" class="comment-text">{{ comment.content }}</x-text>
						</view>
						
						<!-- 回复列表 -->
						<view class="replies-list" v-if="comment.replies && comment.replies.length > 0">
							<view class="reply-item" v-for="reply in comment.replies" :key="reply.id">
								<x-text font-size="12" color="primary">{{ reply.userName }}</x-text>
								<x-text font-size="12" color="#666666" class="mx-4">回复</x-text>
								<x-text font-size="12" color="primary">{{ reply.replyToUserName }}</x-text>
								<x-text font-size="12" color="#333333" class="ml-8">{{ reply.content }}</x-text>
							</view>
						</view>
						
						<x-button @click="replyComment(comment)" 
							color="grey" 
							size="small" 
							width="60" 
							height="24" 
							class="mt-8">
							回复
						</x-button>
					</view>
				</view>
				
				<!-- 空状态 -->
				<x-empty v-else description="暂无评论" icon="chat-1-line" class="mt-32">
					<x-text font-size="14" color="#666666">来发表第一条评论吧</x-text>
				</x-empty>
			</view>
		</x-sheet>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, onMounted } from 'vue'
import type { News, NewsComment } from '@/types/home'

// 页面参数
const newsId = ref<string>('')

// 响应式数据
const newsDetail = ref<News | null>(null)
const commentContent = ref<string>('')
const commentsList = ref<NewsComment[]>([])

// 模拟新闻详情数据
const mockNewsDetail: News = {
	id: '1',
	title: '国务院印发《关于加强基层治理体系和治理能力现代化建设的意见》',
	summary: '为深入贯彻党的二十大精神，加强基层治理体系和治理能力现代化建设，国务院印发相关意见...',
	content: `为深入贯彻党的二十大精神，加强基层治理体系和治理能力现代化建设，国务院近日印发《关于加强基层治理体系和治理能力现代化建设的意见》。

《意见》指出，基层治理是国家治理的基石，统筹推进乡镇（街道）和城乡社区治理，是实现国家治理体系和治理能力现代化的基础工程。要坚持以习近平新时代中国特色社会主义思想为指导，全面贯彻党的二十大精神，坚持和加强党的全面领导，坚持以人民为中心的发展思想，统筹发展和安全，完善党委领导、政府负责、民主协商、社会协同、公众参与、法治保障、科技支撑的社会治理体系。

《意见》明确了基层治理现代化的主要目标：到2025年，基层治理制度更加完善，治理能力明显提升，治理效能不断增强，基层政权巩固和基层政务服务能力显著提高，党组织领导的自治、法治、德治相结合的城乡基层治理体系更加健全，基层治理社会化、法治化、智能化、专业化水平大幅提升。

《意见》提出了六个方面的重点任务：一是完善基层管理体制，理顺条块关系；二是增强基层政权治理能力，夯实治理根基；三是健全基层群众自治制度，激发治理活力；四是推进基层法治建设，营造良好法治环境；五是加强基层德治建设，强化道德约束；六是提升基层智治水平，增强治理效能。`,
	source: '新华社',
	author: '记者张华',
	publishTime: Date.now() - 3600000,
	readCount: 1250,
	category: 'domestic',
	isHot: true,
	imageUrl: 'https://picsum.photos/400/200?random=1',
	tags: ['政策解读', '基层治理', '国务院'],
	isCollected: false
}

// 模拟评论数据
const mockComments: NewsComment[] = [
	{
		id: '1',
		newsId: '1',
		userId: 'u1',
		userName: '政策观察者',
		userAvatar: 'https://picsum.photos/64/64?random=1',
		content: '这个政策对基层治理现代化具有重要意义，期待能够真正落实到位。',
		createTime: Date.now() - 1800000,
		likeCount: 15,
		isLiked: false,
		replies: [
			{
				id: 'r1',
				userId: 'u2',
				userName: '基层工作者',
				userAvatar: 'https://picsum.photos/64/64?random=2',
				content: '确实，我们基层工作者对此充满期待。',
				createTime: Date.now() - 1200000,
				replyToUserId: 'u1',
				replyToUserName: '政策观察者'
			}
		]
	},
	{
		id: '2',
		newsId: '1',
		userId: 'u3',
		userName: '法治研究员',
		userAvatar: 'https://picsum.photos/64/64?random=3',
		content: '基层治理法治化是关键，希望能够建立更完善的法律保障体系。',
		createTime: Date.now() - 3600000,
		likeCount: 8,
		isLiked: true,
		replies: []
	}
]

// 方法
const formatDate = (timestamp: number): string => {
	const now = Date.now()
	const diff = now - timestamp
	const minutes = Math.floor(diff / 60000)
	const hours = Math.floor(diff / 3600000)
	const days = Math.floor(diff / 86400000)

	if (minutes < 60) {
		return `${minutes}分钟前`
	} else if (hours < 24) {
		return `${hours}小时前`
	} else {
		return `${days}天前`
	}
}

const toggleCollect = (): void => {
	if (newsDetail.value) {
		newsDetail.value.isCollected = !newsDetail.value.isCollected
		uni.showToast({
			title: newsDetail.value.isCollected ? '已收藏' : '已取消收藏',
			icon: 'success'
		})
	}
}

const shareNews = (): void => {
	// #ifdef APP
	uni.share({
		provider: 'weixin',
		scene: 'WXSceneSession',
		type: 0,
		href: `https://example.com/news/${newsId.value}`,
		title: newsDetail.value?.title || '',
		summary: newsDetail.value?.summary || '',
		imageUrl: newsDetail.value?.imageUrl || '',
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		}
	})
	// #endif
	
	// #ifndef APP
	uni.showToast({
		title: '分享功能开发中',
		icon: 'none'
	})
	// #endif
}

const submitComment = (): void => {
	if (commentContent.value.trim() === '') return

	const newComment: NewsComment = {
		id: Date.now().toString(),
		newsId: newsId.value,
		userId: 'current_user',
		userName: '当前用户',
		userAvatar: 'https://picsum.photos/64/64?random=999',
		content: commentContent.value.trim(),
		createTime: Date.now(),
		likeCount: 0,
		isLiked: false,
		replies: []
	}

	commentsList.value.unshift(newComment)
	commentContent.value = ''
	
	uni.showToast({
		title: '评论成功',
		icon: 'success'
	})
}

const likeComment = (comment: NewsComment): void => {
	comment.isLiked = !comment.isLiked
	comment.likeCount += comment.isLiked ? 1 : -1
}

const replyComment = (comment: NewsComment): void => {
	uni.showToast({
		title: '回复功能开发中',
		icon: 'none'
	})
}

// 生命周期
onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options as UTSJSONObject
	
	newsId.value = options.getString('id') ?? ''
	
	// 模拟加载新闻详情
	newsDetail.value = mockNewsDetail
	commentsList.value = mockComments
	
	// 设置导航标题
	uni.setNavigationBarTitle({
		title: '新闻详情'
	})
})
</script>

<style>
.news-title {
	line-height: 1.4;
	margin-bottom: 16px;
}

.news-meta {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
	padding-bottom: 16px;
	border-bottom: 1px solid #f5f5f5;
}

.meta-left {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.news-tags {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 16px;
}

.news-image {
	margin-bottom: 20px;
}

.news-content {
	margin-bottom: 20px;
}

.news-text {
	line-height: 1.8;
	color: #333333;
}

.news-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 16px 0;
	border-top: 1px solid #f5f5f5;
}

.comment-section {
	margin-top: 20px;
}

.comment-input {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 20px;
}

.comment-field {
	flex: 1;
	margin-right: 12px;
}

.comments-list {
	margin-top: 20px;
}

.comment-item {
	padding: 16px 0;
	border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
	border-bottom: none;
}

.comment-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12px;
}

.user-info {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.user-details {
	margin-left: 12px;
}

.comment-content {
	margin-bottom: 12px;
}

.comment-text {
	line-height: 1.6;
}

.replies-list {
	background-color: #f8f9fa;
	border-radius: 8px;
	padding: 12px;
	margin: 12px 0;
}

.reply-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 8px;
}

.reply-item:last-child {
	margin-bottom: 0;
}
</style>
