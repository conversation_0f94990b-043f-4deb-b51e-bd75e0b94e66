<template>
	<view class="practice-index">
		<x-sheet class="header-section">
			<x-text font-size="18" class="text-weight-b">练题模块</x-text>
			<x-text font-size="14" color="#666" class="mt-8">选择功能进入对应页面</x-text>
		</x-sheet>
		
		<x-sheet class="menu-section">
			<view class="menu-item" @click="goToTestpapers">
				<x-icon name="file-list-line" size="20" color="primary"></x-icon>
				<view class="menu-content">
					<x-text font-size="16" class="text-weight-b">试卷列表</x-text>
					<x-text font-size="12" color="#666">查看历年真题试卷</x-text>
				</view>
				<x-icon name="arrow-right-s-line" size="16" color="#999"></x-icon>
			</view>
			
			<view class="menu-item" @click="goToExam">
				<x-icon name="edit-line" size="20" color="success"></x-icon>
				<view class="menu-content">
					<x-text font-size="16" class="text-weight-b">直接练题</x-text>
					<x-text font-size="12" color="#666">进入练题页面</x-text>
				</view>
				<x-icon name="arrow-right-s-line" size="16" color="#999"></x-icon>
			</view>
		</x-sheet>
	</view>
</template>

<script setup lang="uts">
import { ref } from 'vue'

// 跳转到试卷列表
const goToTestpapers = () => {
	uni.navigateTo({
		url: '/pages/practice/testpapers'
	})
}

// 跳转到练题页面
const goToExam = () => {
	uni.navigateTo({
		url: '/pages/practice/exam?paperId=demo&paperName=演示试卷&isNewStart=true'
	})
}
</script>

<style>
.practice-index {
	padding: 16px;
}

.header-section {
	margin-bottom: 16px;
	text-align: center;
}

.menu-section {
	padding: 0;
}

.menu-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-content {
	flex: 1;
	margin-left: 12px;
}
</style>