<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class="text-weight-b mb-8">历年真题试卷</x-text>
			<x-text color="#999999">选择试卷开始练题，支持断点续练</x-text>
		</x-sheet>
		
		<x-sheet v-for="paper in testPapers" :key="paper.id" :margin="['16','0']">
			<view class="paper-header">
				<view class="paper-info">
					<x-text font-size="16" class="text-weight-b mb-4">{{ paper.name }}</x-text>
					<view class="paper-meta">
						<x-text font-size="12" color="#666">{{ paper.region }}</x-text>
						<x-text font-size="12" color="#666" class="mx-8">·</x-text>
						<x-text font-size="12" color="#666">{{ paper.year }}年</x-text>
						<x-text font-size="12" color="#666" class="mx-8">·</x-text>
						<x-text font-size="12" color="#666">{{ paper.subject }}</x-text>
					</view>
				</view>
				<view class="paper-tags">
					<x-text v-for="tag in paper.tags" :key="tag" font-size="10" 
						color="primary" class="tag">{{ tag }}</x-text>
				</view>
			</view>
			
			<view class="progress-section">
				<view class="progress-info">
					<x-text font-size="12" color="#666">练题进度</x-text>
					<x-text font-size="12" color="primary" class="ml-8">
						{{ paper.progress.answeredCount }}/{{ paper.progress.totalCount }}
					</x-text>
				</view>
				<x-progress 
					:value="paper.progress.percentage" 
					height="6" 
					color="primary" 
					class="mt-8"
				></x-progress>
			</view>
			
			<view class="action-buttons">
				<x-button 
					@click="startPractice(paper)"
					color="primary" 
					width="45%" 
					height="36"
					font-size="14"
				>
					<x-icon name="play-circle-line" size="16" class="mr-4"></x-icon>
					{{ paper.progress.answeredCount > 0 ? '继续练题' : '开始练题' }}
				</x-button>
				<x-button 
					@click="downloadPaper(paper)"
					color="grey" 
					width="45%" 
					height="36"
					font-size="14"
					:loading="paper.downloadProgress > 0 && paper.downloadProgress < 100"
				>
					<x-icon v-if="!paper.isDownloaded && paper.downloadProgress == 0" 
						name="download-line" size="16" class="mr-4"></x-icon>
					<x-icon v-else-if="paper.isDownloaded" 
						name="check-line" size="16" class="mr-4"></x-icon>
					{{ getDownloadText(paper) }}
				</x-button>
			</view>
		</x-sheet>
		
		<!-- 空状态 -->
		<x-sheet v-if="testPapers.length == 0" class="empty-state">
			<x-text color="#999" class="text-center">暂无试卷数据</x-text>
		</x-sheet>
		
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, reactive, onMounted } from 'vue'
import type { TestPaper } from '@/types/practice'

// 试卷列表数据
const testPapers = ref<TestPaper[]>([])

// 模拟试卷数据
const mockTestPapers: TestPaper[] = [
	{
		id: '1',
		name: '2024年国家公务员考试行政职业能力测验',
		region: '国考',
		year: 2024,
		subject: '行测',
		progress: {
			answeredCount: 45,
			totalCount: 135,
			correctCount: 38,
			percentage: 33.3
		},
		totalQuestions: 135,
		difficulty: 4,
		tags: ['真题', '国考', '最新'],
		isDownloaded: false,
		downloadProgress: 0,
		createTime: '2024-01-15',
		updateTime: '2024-01-15'
	},
	{
		id: '2',
		name: '2023年广东省公务员考试行政职业能力测验',
		region: '广东省考',
		year: 2023,
		subject: '行测',
		progress: {
			answeredCount: 120,
			totalCount: 120,
			correctCount: 95,
			percentage: 100
		},
		totalQuestions: 120,
		difficulty: 3,
		tags: ['真题', '省考', '已完成'],
		isDownloaded: true,
		downloadProgress: 100,
		createTime: '2023-12-20',
		updateTime: '2024-01-10'
	},
	{
		id: '3',
		name: '2024年北京市公务员考试申论',
		region: '北京市考',
		year: 2024,
		subject: '申论',
		progress: {
			answeredCount: 0,
			totalCount: 5,
			correctCount: 0,
			percentage: 0
		},
		totalQuestions: 5,
		difficulty: 5,
		tags: ['真题', '市考', '申论'],
		isDownloaded: false,
		downloadProgress: 0,
		createTime: '2024-01-20',
		updateTime: '2024-01-20'
	}
]

// 开始练题
const startPractice = (paper: TestPaper) => {
	// 检查是否有练题进度
	if (paper.progress > 0) {
		// 有进度，弹窗询问是否继续练题
		uni.showModal({
			title: '练题提示',
			content: `您已完成 ${paper.progress}/${paper.totalQuestions} 题，是否继续练题？`,
			confirmText: '继续练题',
			cancelText: '重新开始',
			success: (res) => {
				if (res.confirm) {
					// 继续练题
					navigateToExam(paper, false)
				} else if (res.cancel) {
					// 重新开始
					navigateToExam(paper, true)
				}
			}
		})
	} else {
		// 没有进度，直接开始
		navigateToExam(paper, true)
	}
}

// 跳转到考试页面
const navigateToExam = (paper: TestPaper, isNewStart: boolean) => {
	uni.navigateTo({
		url: `/pages/practice/exam?paperId=${paper.id}&paperName=${encodeURIComponent(paper.name)}&isNewStart=${isNewStart}`
	})
}

// 下载试卷
const downloadPaper = (paper: TestPaper) => {
	if (paper.isDownloaded) {
		uni.showToast({
			title: '试卷已下载',
			icon: 'success'
		})
		return
	}
	
	if (paper.downloadProgress > 0 && paper.downloadProgress < 100) {
		return // 正在下载中
	}
	
	// 模拟下载进度
	paper.downloadProgress = 1
	const timer = setInterval(() => {
		paper.downloadProgress += Math.random() * 20
		if (paper.downloadProgress >= 100) {
			paper.downloadProgress = 100
			paper.isDownloaded = true
			clearInterval(timer)
			uni.showToast({
				title: '下载完成',
				icon: 'success'
			})
		}
	}, 200)
}

// 获取下载按钮文本
const getDownloadText = (paper: TestPaper): string => {
	if (paper.isDownloaded) {
		return '已下载'
	}
	if (paper.downloadProgress > 0 && paper.downloadProgress < 100) {
		return `${Math.floor(paper.downloadProgress)}%`
	}
	return '下载'
}

// 页面加载
onMounted(() => {
	testPapers.value = mockTestPapers
})
</script>

<style>
.paper-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
}

.paper-info {
	flex: 1;
}

.paper-meta {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 4px;
}

.paper-tags {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

.tag {
	padding: 2px 6px;
	border-radius: 4px;
	background-color: #f0f9ff;
	border: 1px solid #e0f2fe;
}

.progress-section {
	margin-bottom: 16px;
}

.progress-info {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.action-buttons {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.empty-state {
	padding: 60px 20px;
	text-align: center;
}
</style>