<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- 头部搜索和筛选 -->
		<x-sheet>
			<view class="header-section">
				<x-text font-size="20" class="text-weight-b">我的收藏</x-text>
				<x-search placeholder="搜索题目内容" v-model="searchKeyword" @search="onSearch" @clear="onClear"></x-search>
			</view>
			
			<!-- 筛选标签 -->
			<view class="filter-section">
				<x-tabs v-model="currentSubject" :list="subjectList" @change="onSubjectChange"></x-tabs>
			</view>
		</x-sheet>

		<!-- 收藏列表 -->
		<x-sheet :padding="['0']" v-if="filteredCollectionsList.length > 0">
			<view class="collections-list">
				<view class="collection-item" v-for="(collection, index) in filteredCollectionsList" :key="collection.id" 
					@click="viewQuestion(collection)">
					<view class="question-header">
						<view class="question-type">
							<x-text font-size="12" color="primary" class="type-tag">
								{{ getQuestionTypeText(collection.question.type) }}
							</x-text>
							<x-text font-size="12" color="#666666" class="ml-8">
								难度: {{ '★'.repeat(collection.question.difficulty) }}
							</x-text>
						</view>
						<x-text font-size="12" color="#999999">{{ formatDate(collection.collectTime) }}</x-text>
					</view>
					
					<x-text font-size="12" color="primary" class="subject-tag">{{ collection.subject }}</x-text>
					<x-text font-size="12" color="#666666" class="knowledge-point">{{ collection.knowledgePoint }}</x-text>
					
					<view class="question-content">
						<x-text font-size="14" color="#333333" class="question-text">{{ collection.question.content }}</x-text>
					</view>
					
					<view class="question-actions">
						<x-button @click.stop="practiceQuestion(collection)" color="primary" size="small" width="80" height="28">
							<x-icon name="play-line" size="12"></x-icon>
							开始练题
						</x-button>
						<x-button @click.stop="removeCollection(collection)" color="error" size="small" width="80" height="28" class="ml-8">
							<x-icon name="star-fill" size="12"></x-icon>
							取消收藏
						</x-button>
					</view>
				</view>
			</view>
		</x-sheet>

		<!-- 空状态 -->
		<x-empty v-else description="暂无收藏题目" icon="star-line">
			<x-button color="primary" @click="goToPractice">去练题</x-button>
		</x-empty>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, computed, onMounted } from 'vue'
import type { UserCollection, Question } from '@/types/practice'
import { TABS_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"

// 响应式数据
const searchKeyword = ref<string>('')
const currentSubject = ref<string>('all')

// 科目列表
const subjectList = ref<TABS_ITEM_INFO[]>([
	{ title: '全部', id: 'all' },
	{ title: '行政能力', id: 'administrative' },
	{ title: '申论', id: 'essay' },
	{ title: '公共基础', id: 'foundation' },
	{ title: '专业知识', id: 'professional' }
])

// 模拟收藏数据
const collectionsList = ref<UserCollection[]>([
	{
		id: '1',
		questionId: 'q1',
		question: {
			id: 'q1',
			type: 'single',
			content: '中国共产党第二十次全国代表大会于2022年10月16日至22日在北京举行。大会的主题是：高举中国特色社会主义伟大旗帜，全面贯彻新时代中国特色社会主义思想，弘扬伟大建党精神，自信自强、守正创新，踔厉奋发、勇毅前行，为全面建设社会主义现代化国家、全面推进中华民族伟大复兴而团结奋斗。关于党的二十大，下列说法正确的是：',
			options: [
				{ id: 'A', content: '这是中国共产党第二十次全国代表大会', isCorrect: true },
				{ id: 'B', content: '大会于2022年10月在上海举行', isCorrect: false },
				{ id: 'C', content: '大会主题强调了改革开放', isCorrect: false },
				{ id: 'D', content: '大会为期一个月', isCorrect: false }
			],
			correctAnswer: ['A'],
			analysis: '党的二十大于2022年10月16日至22日在北京举行，是中国共产党第二十次全国代表大会。',
			knowledgePoint: '时事政治',
			difficulty: 2,
			isCollected: true,
			userAnswer: null,
			isAnswered: false
		},
		subject: '行政能力',
		knowledgePoint: '时事政治',
		collectTime: Date.now() - 86400000
	},
	{
		id: '2',
		questionId: 'q2',
		question: {
			id: 'q2',
			type: 'multiple',
			content: '根据《中华人民共和国宪法》，下列关于公民基本权利的表述，正确的有：',
			options: [
				{ id: 'A', content: '公民有言论、出版、集会、结社、游行、示威的自由', isCorrect: true },
				{ id: 'B', content: '公民有宗教信仰自由', isCorrect: true },
				{ id: 'C', content: '公民的人身自由不受侵犯', isCorrect: true },
				{ id: 'D', content: '公民有无限制的迁徙自由', isCorrect: false }
			],
			correctAnswer: ['A', 'B', 'C'],
			analysis: '根据宪法规定，公民享有言论自由、宗教信仰自由、人身自由等基本权利。',
			knowledgePoint: '宪法学',
			difficulty: 3,
			isCollected: true,
			userAnswer: null,
			isAnswered: false
		},
		subject: '公共基础',
		knowledgePoint: '宪法学',
		collectTime: Date.now() - 172800000
	}
])

// 过滤后的收藏列表
const filteredCollectionsList = computed<UserCollection[]>(() => {
	let filtered = collectionsList.value

	// 按科目过滤
	if (currentSubject.value !== 'all') {
		filtered = filtered.filter(collection => collection.subject === getSubjectName(currentSubject.value))
	}

	// 按搜索关键词过滤
	if (searchKeyword.value.trim() !== '') {
		const keyword = searchKeyword.value.toLowerCase()
		filtered = filtered.filter(collection =>
			collection.question.content.toLowerCase().includes(keyword) ||
			collection.knowledgePoint.toLowerCase().includes(keyword)
		)
	}

	// 按收藏时间倒序排列
	return filtered.sort((a, b) => b.collectTime - a.collectTime)
})

// 方法
const formatDate = (timestamp: number): string => {
	const now = Date.now()
	const diff = now - timestamp
	const days = Math.floor(diff / 86400000)
	const hours = Math.floor(diff / 3600000)
	const minutes = Math.floor(diff / 60000)

	if (minutes < 60) {
		return `${minutes}分钟前`
	} else if (hours < 24) {
		return `${hours}小时前`
	} else {
		return `${days}天前`
	}
}

const getSubjectName = (subjectId: string): string => {
	const subject = subjectList.value.find(item => item.id === subjectId)
	return subject?.title || ''
}

const getQuestionTypeText = (type: string): string => {
	switch (type) {
		case 'single': return '单选题'
		case 'multiple': return '多选题'
		case 'judge': return '判断题'
		case 'fill': return '填空题'
		case 'essay': return '论述题'
		default: return '未知题型'
	}
}

const onSearch = (keyword: string): void => {
	searchKeyword.value = keyword
}

const onClear = (): void => {
	searchKeyword.value = ''
}

const onSubjectChange = (subjectId: string): void => {
	currentSubject.value = subjectId
}

const viewQuestion = (collection: UserCollection): void => {
	// 跳转到题目详情页面
	uni.navigateTo({
		url: `/pages/practice/exam?questionId=${collection.questionId}`
	})
}

const practiceQuestion = (collection: UserCollection): void => {
	// 开始练题
	uni.navigateTo({
		url: `/pages/practice/exam?questionId=${collection.questionId}&mode=single`
	})
}

const removeCollection = (collection: UserCollection): void => {
	uni.showModal({
		title: '取消收藏',
		content: '确定要取消收藏这道题目吗？',
		success: (res) => {
			if (res.confirm) {
				const index = collectionsList.value.findIndex(item => item.id === collection.id)
				if (index > -1) {
					collectionsList.value.splice(index, 1)
				}
				uni.showToast({
					title: '已取消收藏',
					icon: 'success'
				})
			}
		}
	})
}

const goToPractice = (): void => {
	uni.switchTab({
		url: '/pages/home/<USER>'
	})
}

// 生命周期
onMounted(() => {
	uni.setNavigationBarTitle({
		title: '我的收藏'
	})
})
</script>

<style>
.header-section {
	margin-bottom: 16px;
}

.filter-section {
	margin-top: 16px;
}

.collections-list {
	background-color: #ffffff;
}

.collection-item {
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
	background-color: #ffffff;
}

.collection-item:last-child {
	border-bottom: none;
}

.question-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.question-type {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.type-tag {
	padding: 2px 8px;
	border-radius: 4px;
	background-color: #e3f2fd;
	border: 1px solid #2196f3;
}

.subject-tag {
	padding: 2px 8px;
	border-radius: 4px;
	background-color: #f0f9ff;
	border: 1px solid #0ea5e9;
	margin-bottom: 4px;
	display: inline-block;
}

.knowledge-point {
	margin-bottom: 12px;
}

.question-content {
	margin-bottom: 12px;
}

.question-text {
	line-height: 1.6;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.question-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}
</style>
