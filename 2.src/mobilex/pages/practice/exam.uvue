<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- Header -->
		<x-sheet :margin="['0']" color="white" class="exam-header">
			<view class="header-content">
				<view class="time-progress">
					<x-text font-size="14" color="#666">练题时间: {{ formatTime(examState.elapsedTime) }}</x-text>
					<x-text font-size="14" color="primary" class="ml-16">
						进度: {{ examState.currentQuestionIndex + 1 }}/{{ examState.totalQuestions }}
					</x-text>
				</view>
				<view class="header-actions">
					<x-button @click="openDraft" color="grey" width="60" height="32" font-size="12">
						<x-icon name="edit-line" size="14"></x-icon>
						草稿
					</x-button>
					<x-button @click="openSettings" color="grey" width="60" height="32" font-size="12" class="ml-8">
						<x-icon name="settings-line" size="14"></x-icon>
						设置
					</x-button>
				</view>
			</view>
			<view class="study-mode">
				<x-text font-size="14" color="#333">背题模式</x-text>
				<x-switch v-model="examState.isStudyMode" @change="onStudyModeChange" class="ml-8"></x-switch>
			</view>
		</x-sheet>
		
		<!-- Body -->
		<x-sheet class="question-content">
			<swiper 
				:current="examState.currentQuestionIndex" 
				@change="onSwiperChange"
				class="question-swiper"
				:indicator-dots="false"
				:autoplay="false"
				:duration="300"
			>
				<swiper-item v-for="(question, qIndex) in examState.questions" :key="question.id">
					<view class="question-item">
						<!-- 题目类型 -->
						<view class="question-type">
							<x-text font-size="12" color="primary" class="type-tag">
								{{ getQuestionTypeText(question.type) }}
							</x-text>
							<x-text font-size="12" color="#666" class="ml-8">
								难度: {{ '★'.repeat(question.difficulty) }}
							</x-text>
						</view>
						
						<!-- 题干 -->
						<view class="question-stem">
							<x-text font-size="16" class="line-8">{{ question.content }}</x-text>
						</view>
						
						<!-- 选项 -->
						<view class="options-container">
							<view v-for="(option, index) in question.options" :key="option.id" 
								class="option-item" @click="selectOptionForQuestion(qIndex, option.id)">
								<view class="option-content">
									<view class="option-radio">
										<x-radio v-if="question.type == 'single'" 
											:checked="isOptionSelectedForQuestion(qIndex, option.id)"
											color="primary"
										></x-radio>
										<x-checkbox v-else-if="question.type == 'multiple'" 
											:checked="isOptionSelectedForQuestion(qIndex, option.id)"
											color="primary"
										></x-checkbox>
									</view>
									<x-text font-size="14" class="option-text">
										{{ String.fromCharCode(65 + index) }}. {{ option.content }}
									</x-text>
								</view>
								<!-- 正确答案标识 -->
								<x-icon v-if="(examState.isStudyMode || question.isAnswered) && option.isCorrect" 
									name="check-line" size="16" color="success" class="correct-icon"></x-icon>
							</view>
							
							<!-- 多选题确认按钮 -->
							<x-button v-if="question.type == 'multiple' && !question.isAnswered && qIndex == examState.currentQuestionIndex" 
								@click="confirmMultipleChoiceForQuestion(qIndex)" 
								color="primary" 
								:block="true" 
								height="40"
								class="mt-16"
								:disabled="getSelectedOptionsForQuestion(qIndex).length == 0"
							>
								确认选择
							</x-button>
						</view>
						
						<!-- 答案解析 -->
						<view v-if="examState.isStudyMode || question.isAnswered" class="answer-section">
							<x-sheet color="#f8f9fa" :margin="['16','0']">
								<x-text font-size="14" color="success" class="text-weight-b mb-8">
									正确答案: {{ question.correctAnswer.join(', ') }}
								</x-text>
								<x-text font-size="12" color="primary" class="mb-4">
									知识点: {{ question.knowledgePoint }}
								</x-text>
								<x-text font-size="14" color="#333" class="line-8">
									解析: {{ question.analysis }}
								</x-text>
							</x-sheet>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</x-sheet>
		
		<!-- Footer -->
		<x-sheet :margin="['0']" color="white" class="exam-footer">
			<view class="footer-actions">
				<x-button @click="previousQuestion" 
					color="grey" 
					width="80" 
					height="40"
					:disabled="examState.currentQuestionIndex == 0"
				>
					<x-icon name="arrow-left-line" size="16"></x-icon>
					上一题
				</x-button>
				
				<view class="center-actions">
					<x-button @click="toggleCollect" 
						:color="currentQuestion?.isCollected ? 'warn' : 'grey'" 
						width="60" 
						height="40"
						class="mr-8"
					>
						<x-icon :name="currentQuestion?.isCollected ? 'star-fill' : 'star-line'" size="16"></x-icon>
						收藏
					</x-button>
					<x-button @click="openAnswerCard" 
						color="primary" 
						width="80" 
						height="40"
					>
						<x-icon name="grid-line" size="16"></x-icon>
						答题卡
					</x-button>
				</view>
				
				<x-button @click="nextQuestion" 
					color="primary" 
					width="80" 
					height="40"
					:disabled="examState.currentQuestionIndex >= examState.totalQuestions - 1"
				>
					下一题
					<x-icon name="arrow-right-line" size="16"></x-icon>
				</x-button>
			</view>
		</x-sheet>
		
		<!-- 答题卡弹窗 -->
		<x-overlay v-model:show="showAnswerCard" 
			customContentStyle="width:90%;max-height:70%;"
			custom-style="display: flex;align-items: center;justify-content: center;">
			<x-sheet>
				<x-text font-size="16" class="text-weight-b mb-16">答题卡</x-text>
				<view class="answer-card-grid">
					<view v-for="(card, index) in examState.answerCards" :key="card.questionId" 
						class="answer-card-item" 
						@click="jumpToQuestion(index)">
						<x-text font-size="12" 
							:color="getAnswerCardColor(card)"
							class="text-weight-b">
							{{ index + 1 }}
						</x-text>
					</view>
				</view>
				<x-button @click="showAnswerCard = false" 
					color="grey" 
					:block="true" 
					height="40" 
					class="mt-16">
					关闭
				</x-button>
			</x-sheet>
		</x-overlay>
		
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import type { Question, ExamState, AnswerCard, TestPaper } from '@/types/practice'

// 页面参数
const paperId = ref<string>('')
const paperName = ref<string>('')

// 考试状态
const examState = reactive<ExamState>({
	currentQuestionIndex: 0,
	totalQuestions: 0,
	startTime: Date.now(),
	elapsedTime: 0,
	isStudyMode: false,
	answerCards: [],
	testPaper: {} as TestPaper,
	questions: []
})

// 当前选中的选项
const selectedOptions = ref<string[]>([])

// 显示答题卡
const showAnswerCard = ref<boolean>(false)

// 计时器
let timer: number | null = null

// 当前题目
const currentQuestion = computed<Question | null>(() => {
	if (examState.questions.length == 0 || examState.currentQuestionIndex >= examState.questions.length) {
		return null
	}
	return examState.questions[examState.currentQuestionIndex]
})

// 是否显示答案
const showAnswer = computed<boolean>(() => {
	return examState.isStudyMode || (currentQuestion.value?.isAnswered == true)
})

// 模拟题目数据
const mockQuestions: Question[] = [
	{
		id: '1',
		type: 'single',
		content: '中国共产党第二十次全国代表大会于2022年10月16日至22日在北京举行。大会的主题是：高举中国特色社会主义伟大旗帜，全面贯彻新时代中国特色社会主义思想，弘扬伟大建党精神，自信自强、守正创新，踔厉奋发、勇毅前行，为全面建设社会主义现代化国家、全面推进中华民族伟大复兴而团结奋斗。关于党的二十大，下列说法正确的是：',
		options: [
			{ id: 'A', content: '这是中国共产党第二十次全国代表大会', isCorrect: true },
			{ id: 'B', content: '大会于2022年10月在上海举行', isCorrect: false },
			{ id: 'C', content: '大会主题强调了改革开放', isCorrect: false },
			{ id: 'D', content: '大会为期一个月', isCorrect: false }
		],
		correctAnswer: ['A'],
		analysis: '党的二十大于2022年10月16日至22日在北京举行，是中国共产党第二十次全国代表大会。大会主题强调全面建设社会主义现代化国家、全面推进中华民族伟大复兴。',
		knowledgePoint: '时事政治',
		difficulty: 2,
		isCollected: false,
		userAnswer: null,
		isAnswered: false
	},
	{
		id: '2',
		type: 'multiple',
		content: '根据《中华人民共和国宪法》，下列关于公民基本权利的表述，正确的有：',
		options: [
			{ id: 'A', content: '公民有言论、出版、集会、结社、游行、示威的自由', isCorrect: true },
			{ id: 'B', content: '公民有宗教信仰自由', isCorrect: true },
			{ id: 'C', content: '公民的人身自由不受侵犯', isCorrect: true },
			{ id: 'D', content: '公民有无限制的迁徙自由', isCorrect: false }
		],
		correctAnswer: ['A', 'B', 'C'],
		analysis: '根据宪法规定，公民享有言论自由、宗教信仰自由、人身自由等基本权利，但迁徙自由在我国宪法中并未明确规定为无限制的权利。',
		knowledgePoint: '宪法学',
		difficulty: 3,
		isCollected: false,
		userAnswer: null,
		isAnswered: false
	}
]

// 获取题目类型文本
const getQuestionTypeText = (type: string): string => {
	switch (type) {
		case 'single': return '单选题'
		case 'multiple': return '多选题'
		case 'judge': return '判断题'
		case 'fill': return '填空题'
		case 'essay': return '论述题'
		default: return '未知题型'
	}
}

// 格式化时间
const formatTime = (seconds: number): string => {
	const hours = Math.floor(seconds / 3600)
	const minutes = Math.floor((seconds % 3600) / 60)
	const secs = seconds % 60
	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// Swiper变化事件
const onSwiperChange = (e: any) => {
	examState.currentQuestionIndex = e.detail.current
	loadCurrentQuestion()
}

// 检查选项是否被选中（针对特定题目）
const isOptionSelectedForQuestion = (questionIndex: number, optionId: string): boolean => {
	const question = examState.questions[questionIndex]
	if (!question) return false
	if (question.userAnswer != null) {
		return question.userAnswer.includes(optionId)
	}
	if (questionIndex === examState.currentQuestionIndex) {
		return selectedOptions.value.includes(optionId)
	}
	return false
}



// 获取特定题目的选中选项
const getSelectedOptionsForQuestion = (questionIndex: number): string[] => {
	const question = examState.questions[questionIndex]
	if (!question) return []
	if (question.userAnswer != null) {
		return question.userAnswer
	}
	if (questionIndex === examState.currentQuestionIndex) {
		return selectedOptions.value
	}
	return []
}

// 选择选项（针对特定题目）
const selectOptionForQuestion = (questionIndex: number, optionId: string) => {
	const question = examState.questions[questionIndex]
	if (!question || (question.isAnswered && !examState.isStudyMode)) {
		return // 已答题且非背题模式不允许修改
	}
	
	// 如果不是当前题目，需要先切换到该题目
	if (questionIndex !== examState.currentQuestionIndex) {
		examState.currentQuestionIndex = questionIndex
		loadCurrentQuestion()
	}
	
	if (question.type == 'single') {
		// 单选题直接确认
		selectedOptions.value = [optionId]
		confirmAnswerForQuestion(questionIndex)
	} else if (question.type == 'multiple') {
		// 多选题切换选中状态
		const index = selectedOptions.value.indexOf(optionId)
		if (index > -1) {
			selectedOptions.value.splice(index, 1)
		} else {
			selectedOptions.value.push(optionId)
		}
	}
}



// 确认多选题答案（针对特定题目）
const confirmMultipleChoiceForQuestion = (questionIndex: number) => {
	const question = examState.questions[questionIndex]
	if (!question || question.type !== 'multiple') return
	confirmAnswerForQuestion(questionIndex)
}



// 确认答案（针对特定题目）
const confirmAnswerForQuestion = (questionIndex: number) => {
	const question = examState.questions[questionIndex]
	if (!question) return
	
	const answers = questionIndex === examState.currentQuestionIndex ? selectedOptions.value : (question.userAnswer || [])
	question.userAnswer = [...answers]
	question.isAnswered = true
	
	// 更新答题卡
	const answerCard = examState.answerCards[questionIndex]
	answerCard.userAnswer = [...answers]
	answerCard.isAnswered = true
	answerCard.isCorrect = JSON.stringify(answers.sort()) == JSON.stringify(question.correctAnswer.sort())
}





// 上一题
const previousQuestion = () => {
	if (examState.currentQuestionIndex > 0) {
		examState.currentQuestionIndex--
		loadCurrentQuestion()
	}
}

// 下一题
const nextQuestion = () => {
	if (examState.currentQuestionIndex < examState.totalQuestions - 1) {
		examState.currentQuestionIndex++
		loadCurrentQuestion()
	}
}

// 加载当前题目
const loadCurrentQuestion = () => {
	if (currentQuestion.value?.userAnswer != null) {
		selectedOptions.value = [...currentQuestion.value.userAnswer]
	} else {
		selectedOptions.value = []
	}
}

// 切换收藏
const toggleCollect = () => {
	if (currentQuestion.value != null) {
		currentQuestion.value.isCollected = !currentQuestion.value.isCollected
		uni.showToast({
			title: currentQuestion.value.isCollected ? '已收藏' : '已取消收藏',
			icon: 'success'
		})
	}
}

// 背题模式切换
const onStudyModeChange = (value: boolean) => {
	examState.isStudyMode = value
}

// 打开草稿
const openDraft = () => {
	uni.showToast({
		title: '草稿功能开发中',
		icon: 'none'
	})
}

// 打开设置
const openSettings = () => {
	uni.showToast({
		title: '设置功能开发中',
		icon: 'none'
	})
}

// 打开答题卡
const openAnswerCard = () => {
	showAnswerCard.value = true
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
	examState.currentQuestionIndex = index
	loadCurrentQuestion()
	showAnswerCard.value = false
}

// 获取答题卡颜色
const getAnswerCardColor = (card: AnswerCard): string => {
	if (!card.isAnswered) return '#ccc'
	if (card.isCorrect == true) return 'success'
	if (card.isCorrect == false) return 'error'
	return 'primary'
}

// 初始化考试数据
const initExamData = () => {
	// 模拟从API获取数据
	examState.questions = mockQuestions
	examState.totalQuestions = mockQuestions.length
	
	// 初始化答题卡
	examState.answerCards = mockQuestions.map(question => ({
		questionId: question.id,
		userAnswer: null,
		isCorrect: null,
		isCollected: false,
		isAnswered: false
	}))
	
	loadCurrentQuestion()
}

// 启动计时器
const startTimer = () => {
	timer = setInterval(() => {
		examState.elapsedTime = Math.floor((Date.now() - examState.startTime) / 1000)
	}, 1000)
}

// 页面加载
onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options as UTSJSONObject
	
	paperId.value = options.getString('paperId') ?? ''
	paperName.value = decodeURIComponent(options.getString('paperName') ?? '')
	
	// 设置导航标题
	uni.setNavigationBarTitle({
		title: paperName.value || '练题'
	})
	
	initExamData()
	startTimer()
	loadCurrentQuestion()
})

// 页面卸载
onUnmounted(() => {
	if (timer != null) {
		clearInterval(timer)
	}
})
</script>

<style>
.exam-header {
	padding: 12px 16px;
	border-bottom: 1px solid #f0f0f0;
}

.header-content {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.time-progress {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.header-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.study-mode {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.question-content {
	flex: 1;
	padding: 0;
	overflow: hidden;
}

.question-swiper {
	height: 100%;
	width: 100%;
}

.question-item {
	padding: 16px;
	height: 100%;
	overflow-y: auto;
}

.question-type {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 16px;
}

.type-tag {
	padding: 4px 8px;
	border-radius: 4px;
	background-color: #e3f2fd;
	border: 1px solid #2196f3;
}

.question-stem {
	margin-bottom: 20px;
	padding: 16px;
	background-color: #fafafa;
	border-radius: 8px;
}

.options-container {
	margin-bottom: 16px;
}

.option-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 12px;
	margin-bottom: 8px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background-color: #ffffff;
}

.option-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	flex: 1;
}

.option-radio {
	margin-right: 12px;
}

.option-text {
	flex: 1;
	line-height: 1.5;
}

.correct-icon {
	margin-left: 8px;
}

.answer-section {
	margin-top: 20px;
}

.exam-footer {
	padding: 12px 16px;
	border-top: 1px solid #f0f0f0;
}

.footer-actions {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}

.center-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.answer-card-grid {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 16px;
}

.answer-card-item {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	background-color: #ffffff;
}
</style>