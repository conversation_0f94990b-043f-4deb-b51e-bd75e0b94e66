<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- 统计概览 -->
		<x-sheet>
			<x-text font-size="18" class="text-weight-b mb-16">练题统计</x-text>
			<x-grid :col="4" :border="false">
				<x-grid-item v-for="(stat, key) in statsData" :key="key">
					<view class="stat-item">
						<x-text font-size="18" class="text-weight-b" color="primary">{{ stat.value }}</x-text>
						<x-text font-size="12" color="#666666">{{ stat.label }}</x-text>
					</view>
				</x-grid-item>
			</x-grid>
		</x-sheet>

		<!-- 筛选区域 -->
		<x-sheet>
			<view class="filter-section">
				<x-tabs v-model="currentSubject" :list="subjectList" @change="onSubjectChange"></x-tabs>
			</view>
		</x-sheet>

		<!-- 练题记录列表 -->
		<x-sheet :padding="['0']" v-if="filteredRecordsList.length > 0">
			<view class="records-list">
				<view class="record-item" v-for="(record, index) in filteredRecordsList" :key="record.id" 
					@click="viewRecordDetail(record)">
					<view class="record-header">
						<view class="record-title">
							<x-text font-size="14" class="text-weight-b">{{ record.testPaperName }}</x-text>
							<x-text font-size="12" color="primary" class="mode-tag">{{ getModeText(record.mode) }}</x-text>
						</view>
						<x-text font-size="12" color="#999999">{{ formatDate(record.startTime) }}</x-text>
					</view>
					
					<x-text font-size="12" color="#666666" class="subject-text">{{ record.subject }}</x-text>
					
					<view class="record-stats">
						<view class="stat-row">
							<view class="stat-item-small">
								<x-text font-size="12" color="#666666">题目数量</x-text>
								<x-text font-size="14" class="text-weight-b">{{ record.totalQuestions }}</x-text>
							</view>
							<view class="stat-item-small">
								<x-text font-size="12" color="#666666">已答题数</x-text>
								<x-text font-size="14" class="text-weight-b">{{ record.answeredQuestions }}</x-text>
							</view>
							<view class="stat-item-small">
								<x-text font-size="12" color="#666666">正确数</x-text>
								<x-text font-size="14" class="text-weight-b" color="success">{{ record.correctQuestions }}</x-text>
							</view>
							<view class="stat-item-small">
								<x-text font-size="12" color="#666666">正确率</x-text>
								<x-text font-size="14" class="text-weight-b" :color="getAccuracyColor(record.accuracy)">
									{{ record.accuracy.toFixed(1) }}%
								</x-text>
							</view>
						</view>
						
						<view class="progress-row">
							<x-text font-size="12" color="#666666">用时: {{ formatTime(record.timeSpent) }}</x-text>
							<view class="completion-status">
								<x-icon :name="record.isCompleted ? 'checkbox-circle-fill' : 'time-line'" 
									:color="record.isCompleted ? 'success' : 'warning'" size="14"></x-icon>
								<x-text font-size="12" :color="record.isCompleted ? 'success' : 'warning'" class="ml-4">
									{{ record.isCompleted ? '已完成' : '未完成' }}
								</x-text>
							</view>
						</view>
					</view>
					
					<view class="record-actions">
						<x-button @click.stop="continueRecord(record)" 
							:color="record.isCompleted ? 'grey' : 'primary'" 
							size="small" width="80" height="28">
							{{ record.isCompleted ? '查看详情' : '继续练题' }}
						</x-button>
						<x-button @click.stop="deleteRecord(record)" color="error" size="small" width="60" height="28" class="ml-8">
							<x-icon name="delete-bin-line" size="12"></x-icon>
							删除
						</x-button>
					</view>
				</view>
			</view>
		</x-sheet>

		<!-- 空状态 -->
		<x-empty v-else description="暂无练题记录" icon="file-list-line">
			<x-button color="primary" @click="goToPractice">开始练题</x-button>
		</x-empty>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, computed, onMounted } from 'vue'
import type { PracticeRecord } from '@/types/practice'
import { SelectItem } from '@/types/index'
import { TABS_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"

// 响应式数据
const currentSubject = ref<string>('all')

// 统计数据
const statsData = ref<SelectItem[]>([
	{ label: '总练题数', value: '1,234' },
	{ label: '总用时', value: '156h' },
	{ label: '平均正确率', value: '85.2%' },
	{ label: '连续天数', value: '15天' }
])

// 科目列表
const subjectList = ref<TABS_ITEM_INFO[]>([
	{ title: '全部', id: 'all' },
	{ title: '行政能力', id: 'administrative' },
	{ title: '申论', id: 'essay' },
	{ title: '公共基础', id: 'foundation' },
	{ title: '专业知识', id: 'professional' }
])

// 模拟练题记录数据
const recordsList = ref<PracticeRecord[]>([
	{
		id: '1',
		testPaperId: 'tp1',
		testPaperName: '2023年国家公务员考试行测真题',
		subject: '行政能力',
		mode: 'exam',
		startTime: Date.now() - 86400000,
		endTime: Date.now() - 82800000,
		totalQuestions: 100,
		answeredQuestions: 100,
		correctQuestions: 85,
		accuracy: 85.0,
		timeSpent: 3600,
		isCompleted: true
	},
	{
		id: '2',
		testPaperId: 'tp2',
		testPaperName: '公共基础知识模拟题',
		subject: '公共基础',
		mode: 'random',
		startTime: Date.now() - 172800000,
		endTime: 0,
		totalQuestions: 50,
		answeredQuestions: 32,
		correctQuestions: 28,
		accuracy: 87.5,
		timeSpent: 1800,
		isCompleted: false
	},
	{
		id: '3',
		testPaperId: 'tp3',
		testPaperName: '我的错题集',
		subject: '行政能力',
		mode: 'wrong',
		startTime: Date.now() - 259200000,
		endTime: Date.now() - 257400000,
		totalQuestions: 25,
		answeredQuestions: 25,
		correctQuestions: 20,
		accuracy: 80.0,
		timeSpent: 1800,
		isCompleted: true
	}
])

// 过滤后的记录列表
const filteredRecordsList = computed<PracticeRecord[]>(() => {
	let filtered = recordsList.value

	// 按科目过滤
	if (currentSubject.value !== 'all') {
		filtered = filtered.filter(record => record.subject === getSubjectName(currentSubject.value))
	}

	// 按开始时间倒序排列
	return filtered.sort((a, b) => b.startTime - a.startTime)
})

// 方法
const formatDate = (timestamp: number): string => {
	const date = new Date(timestamp)
	const now = new Date()
	const diff = now.getTime() - timestamp
	const days = Math.floor(diff / 86400000)

	if (days === 0) {
		return '今天'
	} else if (days === 1) {
		return '昨天'
	} else if (days < 7) {
		return `${days}天前`
	} else {
		return `${date.getMonth() + 1}月${date.getDate()}日`
	}
}

const formatTime = (seconds: number): string => {
	const hours = Math.floor(seconds / 3600)
	const minutes = Math.floor((seconds % 3600) / 60)
	const secs = seconds % 60

	if (hours > 0) {
		return `${hours}时${minutes}分`
	} else if (minutes > 0) {
		return `${minutes}分${secs}秒`
	} else {
		return `${secs}秒`
	}
}

const getSubjectName = (subjectId: string): string => {
	const subject = subjectList.value.find(item => item.id === subjectId)
	return subject?.title || ''
}

const getModeText = (mode: string): string => {
	switch (mode) {
		case 'sequence': return '顺序练题'
		case 'random': return '随机练题'
		case 'exam': return '历年真题'
		case 'wrong': return '错题练习'
		case 'collection': return '收藏练习'
		default: return '练题模式'
	}
}

const getAccuracyColor = (accuracy: number): string => {
	if (accuracy >= 90) return 'success'
	if (accuracy >= 70) return 'warning'
	return 'error'
}

const onSubjectChange = (subjectId: string): void => {
	currentSubject.value = subjectId
}

const viewRecordDetail = (record: PracticeRecord): void => {
	// 查看练题记录详情
	uni.navigateTo({
		url: `/pages/practice/record-detail?recordId=${record.id}`
	})
}

const continueRecord = (record: PracticeRecord): void => {
	if (record.isCompleted) {
		// 查看详情
		viewRecordDetail(record)
	} else {
		// 继续练题
		uni.navigateTo({
			url: `/pages/practice/exam?paperId=${record.testPaperId}&recordId=${record.id}`
		})
	}
}

const deleteRecord = (record: PracticeRecord): void => {
	uni.showModal({
		title: '删除记录',
		content: '确定要删除这条练题记录吗？',
		success: (res) => {
			if (res.confirm) {
				const index = recordsList.value.findIndex(item => item.id === record.id)
				if (index > -1) {
					recordsList.value.splice(index, 1)
				}
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
			}
		}
	})
}

const goToPractice = (): void => {
	uni.switchTab({
		url: '/pages/home/<USER>'
	})
}

// 生命周期
onMounted(() => {
	uni.setNavigationBarTitle({
		title: '练题记录'
	})
})
</script>

<style>
.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 8px;
}

.filter-section {
	margin-top: 0;
}

.records-list {
	background-color: #ffffff;
}

.record-item {
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
	background-color: #ffffff;
}

.record-item:last-child {
	border-bottom: none;
}

.record-header {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 8px;
}

.record-title {
	display: flex;
	flex-direction: row;
	align-items: center;
	flex: 1;
}

.mode-tag {
	padding: 2px 8px;
	border-radius: 4px;
	background-color: #f0f9ff;
	border: 1px solid #0ea5e9;
	margin-left: 8px;
}

.subject-text {
	margin-bottom: 12px;
}

.record-stats {
	margin-bottom: 12px;
}

.stat-row {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-bottom: 8px;
}

.stat-item-small {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.progress-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.completion-status {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.record-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}
</style>
