<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- 头部搜索和筛选 -->
		<x-sheet>
			<view class="header-section">
				<x-text font-size="20" class="text-weight-b">我的笔记</x-text>
				<x-search placeholder="搜索笔记内容" v-model="searchKeyword" @search="onSearch" @clear="onClear"></x-search>
			</view>
			
			<!-- 筛选标签 -->
			<view class="filter-section">
				<x-tabs v-model="currentSubject" :list="subjectList" @change="onSubjectChange"></x-tabs>
			</view>
		</x-sheet>

		<!-- 笔记列表 -->
		<x-sheet :padding="['0']" v-if="filteredNotesList.length > 0">
			<view class="notes-list">
				<view class="note-item" v-for="(note, index) in filteredNotesList" :key="note.id" @click="viewNoteDetail(note)">
					<view class="note-header">
						<x-text font-size="14" color="primary" class="subject-tag">{{ note.subject }}</x-text>
						<x-text font-size="12" color="#999999">{{ formatDate(note.createTime) }}</x-text>
					</view>
					
					<x-text font-size="12" color="#666666" class="knowledge-point">{{ note.knowledgePoint }}</x-text>
					
					<view class="note-content">
						<x-text font-size="14" color="#333333" class="note-text">{{ note.content }}</x-text>
					</view>
					
					<view class="note-actions">
						<x-button @click.stop="editNote(note)" color="primary" size="small" width="60" height="28">
							<x-icon name="edit-line" size="12"></x-icon>
							编辑
						</x-button>
						<x-button @click.stop="deleteNote(note)" color="error" size="small" width="60" height="28" class="ml-8">
							<x-icon name="delete-bin-line" size="12"></x-icon>
							删除
						</x-button>
					</view>
				</view>
			</view>
		</x-sheet>

		<!-- 空状态 -->
		<x-empty v-else description="暂无笔记记录" icon="book-line">
			<x-button color="primary" @click="goToPractice">去练题</x-button>
		</x-empty>

		<!-- 编辑笔记弹窗 -->
		<x-overlay v-model:show="showEditDialog" 
			customContentStyle="width:90%;max-height:70%;"
			custom-style="display: flex;align-items: center;justify-content: center;">
			<x-sheet>
				<x-text font-size="16" class="text-weight-b mb-16">编辑笔记</x-text>
				<x-input v-model="editingNote.content" 
					type="textarea" 
					placeholder="请输入笔记内容" 
					:maxlength="500"
					height="120"
					class="mb-16">
				</x-input>
				<view class="dialog-actions">
					<x-button @click="showEditDialog = false" color="grey" width="80" height="36">取消</x-button>
					<x-button @click="saveNote" color="primary" width="80" height="36" class="ml-8">保存</x-button>
				</view>
			</x-sheet>
		</x-overlay>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup lang="uts">
import { ref, computed, onMounted } from 'vue'
import type { UserNote } from '@/types/practice'
import { TABS_ITEM_INFO } from "@/uni_modules/tmx-ui/interface.uts"

// 响应式数据
const searchKeyword = ref<string>('')
const currentSubject = ref<string>('all')
const showEditDialog = ref<boolean>(false)
const editingNote = ref<UserNote>({} as UserNote)

// 科目列表
const subjectList = ref<TABS_ITEM_INFO[]>([
	{ title: '全部', id: 'all' },
	{ title: '行政能力', id: 'administrative' },
	{ title: '申论', id: 'essay' },
	{ title: '公共基础', id: 'foundation' },
	{ title: '专业知识', id: 'professional' }
])

// 模拟笔记数据
const notesList = ref<UserNote[]>([
	{
		id: '1',
		questionId: 'q1',
		content: '这道题考查的是行政法的基本原则，需要重点记忆依法行政、合理行政、程序正当、高效便民、诚实守信、权责统一六个原则。',
		subject: '行政能力',
		knowledgePoint: '行政法基本原则',
		createTime: Date.now() - 86400000,
		updateTime: Date.now() - 86400000
	},
	{
		id: '2',
		questionId: 'q2',
		content: '申论写作要注意结构完整，论点明确，论据充分。开头要简洁有力，结尾要升华主题。',
		subject: '申论',
		knowledgePoint: '申论写作技巧',
		createTime: Date.now() - 172800000,
		updateTime: Date.now() - 172800000
	},
	{
		id: '3',
		questionId: 'q3',
		content: '马克思主义哲学的基本观点：物质决定意识，意识对物质具有能动作用。实践是认识的基础。',
		subject: '公共基础',
		knowledgePoint: '马克思主义哲学',
		createTime: Date.now() - 259200000,
		updateTime: Date.now() - 259200000
	}
])

// 过滤后的笔记列表
const filteredNotesList = computed<UserNote[]>(() => {
	let filtered = notesList.value

	// 按科目过滤
	if (currentSubject.value !== 'all') {
		filtered = filtered.filter(note => note.subject === getSubjectName(currentSubject.value))
	}

	// 按搜索关键词过滤
	if (searchKeyword.value.trim() !== '') {
		const keyword = searchKeyword.value.toLowerCase()
		filtered = filtered.filter(note =>
			note.content.toLowerCase().includes(keyword) ||
			note.knowledgePoint.toLowerCase().includes(keyword)
		)
	}

	// 按创建时间倒序排列
	return filtered.sort((a, b) => b.createTime - a.createTime)
})

// 方法
const formatDate = (timestamp: number): string => {
	const now = Date.now()
	const diff = now - timestamp
	const days = Math.floor(diff / 86400000)
	const hours = Math.floor(diff / 3600000)
	const minutes = Math.floor(diff / 60000)

	if (minutes < 60) {
		return `${minutes}分钟前`
	} else if (hours < 24) {
		return `${hours}小时前`
	} else {
		return `${days}天前`
	}
}

const getSubjectName = (subjectId: string): string => {
	const subject = subjectList.value.find(item => item.id === subjectId)
	return subject?.title || ''
}

const onSearch = (keyword: string): void => {
	searchKeyword.value = keyword
}

const onClear = (): void => {
	searchKeyword.value = ''
}

const onSubjectChange = (subjectId: string): void => {
	currentSubject.value = subjectId
}

const viewNoteDetail = (note: UserNote): void => {
	// 跳转到题目详情页面
	uni.navigateTo({
		url: `/pages/practice/exam?questionId=${note.questionId}&noteId=${note.id}`
	})
}

const editNote = (note: UserNote): void => {
	editingNote.value = { ...note }
	showEditDialog.value = true
}

const saveNote = (): void => {
	// 保存笔记
	const index = notesList.value.findIndex(note => note.id === editingNote.value.id)
	if (index > -1) {
		notesList.value[index].content = editingNote.value.content
		notesList.value[index].updateTime = Date.now()
	}
	
	showEditDialog.value = false
	uni.showToast({
		title: '保存成功',
		icon: 'success'
	})
}

const deleteNote = (note: UserNote): void => {
	uni.showModal({
		title: '删除笔记',
		content: '确定要删除这条笔记吗？',
		success: (res) => {
			if (res.confirm) {
				const index = notesList.value.findIndex(item => item.id === note.id)
				if (index > -1) {
					notesList.value.splice(index, 1)
				}
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
			}
		}
	})
}

const goToPractice = (): void => {
	uni.switchTab({
		url: '/pages/home/<USER>'
	})
}

// 生命周期
onMounted(() => {
	uni.setNavigationBarTitle({
		title: '我的笔记'
	})
})
</script>

<style>
.header-section {
	margin-bottom: 16px;
}

.filter-section {
	margin-top: 16px;
}

.notes-list {
	background-color: #ffffff;
}

.note-item {
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
	background-color: #ffffff;
}

.note-item:last-child {
	border-bottom: none;
}

.note-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.subject-tag {
	padding: 2px 8px;
	border-radius: 4px;
	background-color: #e3f2fd;
	border: 1px solid #2196f3;
}

.knowledge-point {
	margin-bottom: 12px;
}

.note-content {
	margin-bottom: 12px;
}

.note-text {
	line-height: 1.6;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.note-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}

.dialog-actions {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}
</style>
