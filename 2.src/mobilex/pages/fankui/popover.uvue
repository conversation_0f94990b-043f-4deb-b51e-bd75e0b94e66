<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
		<navigation-bar :background-color="xThemeConfigNavBgColor" :front-color="xThemeConfigNavFontColor"></navigation-bar>
	</page-meta>
	<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">汽泡菜单 Popover</x-text>
			<x-text color="#999999">
				方向由上三个和下三个方位共6个。不提供左和右，因为在移动端用处不大。采用插槽布局弹出内容灵活自由度极高。同时可以插槽触发以及变量控制关闭。
				警告:如果你弹出的内容比如文本,它会自动宽和高,是个特殊组件在web上,此类需要固定下宽.
			</x-text>
		</x-sheet>

		<x-sheet class="flex flex-row flex-row-center-between">
			<x-popover :show-triangle="true" position="bl">
				<x-button color="error" round="64" :iconBtn="true" icon="menu-unfold-fill"></x-button>
				<template #menu>
					<view style="width:140px">
						<x-cell v-for="(item,index) in menuCount" :key="index" :show-bottom-border="index!=menuCount-1"
							:card="false" :title="'菜单-'+item"></x-cell>
					</view>

				</template>
			</x-popover>
			<x-popover mask-bg-color="rgba(0,0,0,0.3)" :show-triangle="true" triangle-color="black" triangle-dark-color="black">
				<x-button round="64" :iconBtn="true" icon="menu-line"></x-button>
				<template #menu>
					<view style="width:140px">
						<x-cell color="black" dark-color="black" bottom-border-color="#1f1f1f" title-color="white" icon-color="white"
							v-for="(item,index) in menuCount" :key="index" :show-bottom-border="index!=menuCount-1"
							:card="false" :title="'菜单-'+item"></x-cell>
					</view>

				</template>
			</x-popover>
			<x-popover :show-triangle="true" position="br" triangle-color="success" triangle-dark-color="success">
				<x-button round="64" color="success" :iconBtn="true" icon="more-2-line"></x-button>
				<template #menu>
					<view style="width:140px">
						<x-cell color="success" icon-color="white" dark-color="success" linkColor="white" linkDarkColor="white" titleColor="white" bottomBorderColor="rgba(255,255,255,0.1)" v-for="(item,index) in menuCount" :key="index" :show-bottom-border="index!=menuCount-1"
							:card="false" :title="'菜单-'+item"></x-cell>
					</view>
				</template>
			</x-popover>
		</x-sheet>

		<x-sheet>
			<x-text font-size="18" class=" text-weight-b ">默认弹出</x-text>
		</x-sheet>
		<x-sheet class="flex flex-row flex-row-center-center">

			<x-popover position="bc" v-model="show" :show-triangle="true" triangle-color="#333" triangle-dark-color="#333">
				<text class="text-red">点击我也能打开菜单</text>
				<template #menu>
					<view style="width:180px">
						<x-cell url="text" color="#333" dark-color="#333" title-color="white" icon-color="white"
							v-for="(item,index) in menuCount" :key="index" bottom-border-color="#474747"
							:show-bottom-border="index!=menuCount-1" :card="false" :title="'菜单-'+item"></x-cell>
					</view>
				</template>
			</x-popover>
			<!-- <x-button class="ml-32" @click="show=true">变量控制</x-button> -->
		</x-sheet>
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b ">弹出方向</x-text>
		</x-sheet>
		<x-sheet>
			<view class="flex flex-row flex-row-center-between mb-32">
				<x-popover position="bl">
					<x-text>底左对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333" :margin="['0']">
							<x-text>菜单可随意布局</x-text>
							<x-button>任意布局哦</x-button>
						</x-sheet>
					</template>
				</x-popover>
				<x-popover>
					<x-text>底中对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333" :margin="['0']" ><x-text >菜单可随意布局</x-text></x-sheet>
						
					</template>
				</x-popover>
				<x-popover position="br">
					<x-text>底右对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333" width="120" :margin="['0']">
							<x-text>菜单可随意布局</x-text>
						</x-sheet>
					</template>
				</x-popover>
			</view>
			<view class="flex flex-row flex-row-center-between">
				<x-popover position="tl">
					<x-text>上左对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333" :margin="['0']"><x-text>菜单可随意布局</x-text></x-sheet>
					</template>
				</x-popover>
				<x-popover position="tc">
					<x-text>上中对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333" :margin="['0']"><x-text>菜单可随意布局</x-text></x-sheet>
					</template>
				</x-popover>
				<x-popover position="tr">
					<x-text>上右对齐</x-text>
					<template #menu>
						<x-sheet dark-color="#333"  width="120"  :margin="['0']"><x-text>菜单可随意布局</x-text></x-sheet>
					</template>
				</x-popover>
			</view>

		</x-sheet>
		
		<view style="height: 100px;"></view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	export default {
		data() {
			return {
				menuCount: 4,
				show: true
			};
		},
		onLoad() {

		},
		methods: {

		}
	}
</script>

<style lang="scss">

</style>