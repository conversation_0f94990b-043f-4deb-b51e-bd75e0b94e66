<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
		<navigation-bar :background-color="xThemeConfigNavBgColor" :front-color="xThemeConfigNavFontColor"></navigation-bar>
	</page-meta>
	<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">左滑菜单 SwitchSlider</x-text>
			<x-text color="#999999">
				通过插槽可完全自定义布局，自由度非常高。如果子菜单无法定宽或者被挤夺请写style:flex-shrink: 0;避免。
			</x-text>
		</x-sheet>
		<x-sheet :padding="['0px']">
			<x-switch-slider
			:status="item.getBoolean('opened')"
			@open="onopen(index)"
			@close="onclose(index)"
			height="64" @disabledScrollChange="disablechange" @click="onclick(index)"
				v-for="(item,index) in testList" :key="index" :disabled="item.getBoolean('disabled')">
				<x-cell leftSize='40' min-height="64" :showBottomBorder="false" :card="false"
					:title="item.getString('title')" :desc="item.getString('subtext')" icon='F264'>
					
					<template #avatar>
						<x-sheet :margin="['0']" :round="['8']" :padding="['0']" color="primary" width="100%" height="100%" class="felx flex-center">
							<x-icon font-size="24" code="F264" color="white"></x-icon>
						</x-sheet>
					</template>
					
				</x-cell>
				<template #menu>
					<view class="flex flex-row flex-row-center-end" style="height: 100%;">
						<view @click="share" class="flex flex-row flex-row-center-center"
							style="width:110px;background:black;height: 100%;">
							<text class="text-white">分享</text>
						</view>
						<view @click="remvoe" class="flex flex-row flex-row-center-center"
							style="width:110px;background:red;height: 100%;">
							<text class="text-white">删除</text>
						</view>
					</view>
				</template>
			</x-switch-slider>
		</x-sheet>
		
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b">双向绑定</x-text>
		</x-sheet>

		<x-sheet :padding="['0px']" >
			<x-switch-slider
			v-model:status="(showLeft as boolean)"
			height="64"
			>
				<x-sheet :round="['0']" :margin="['0']" :padding="['24','0']" height="64" class="flex flex-row flex-row-center-start">
					<x-text>
						双向绑定{{showLeft}}
					</x-text>
					
				</x-sheet>
				
				<template #menu>
					<view class="flex flex-row flex-row-center-end" style="height: 100%;">
						<view class="flex flex-row flex-row-center-center"
							style="width:110px;background:black;height: 100%;">
							<text class="text-white">分享</text>
						</view>
						<view class="flex flex-row flex-row-center-center"
							style="width:110px;background:red;height: 100%;">
							<text class="text-white">删除</text>
						</view>
					</view>
				</template>
			</x-switch-slider>
		</x-sheet>

		<view style="height: 1500px;"></view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	export default {
		data() {
			return {
				showLeft:false,
				disabledScoll: false,
				testList: [] as UTSJSONObject[]
			};
		},
		beforeMount() {
			for (let i = 0; i < 6; i++) {
				this.testList.push({
					id: Math.random().toString(16).substring(0, 12),
					index: i,
					disabled: i == 1,
					title: i == 1 ? "被禁用" : ("我是对话标题+" + i.toString()),
					subtext: "演示手动管理关闭状态",
					opened:false
				} as UTSJSONObject)
			}

		},
		onLoad() {

		},
		methods: {

			onopen(index:number){
				let oldlist = this.testList.slice(0)
				oldlist = oldlist.map((el:UTSJSONObject,from:number):UTSJSONObject =>{
					if(from!=index){
						el.set("opened",false)
					}else{
						el.set("opened",true)
					}
					return el;
				})
				this.testList = oldlist.slice(0)
			},
			onclose(index:number){
				
			},
			remvoe() {
				console.log('remove')
			},
			share() {
				console.log('share')
			},
			onclick(index : number) {
				uni.showToast({ title: "你点击了：" + index.toString() })
			},
			disablechange(disabled : boolean) {
				this.disabledScoll = disabled;
			}
		}
	}
</script>

<style lang="scss">

</style>