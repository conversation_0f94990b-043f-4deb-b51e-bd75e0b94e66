<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1" >
	<!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
		<navigation-bar :background-color="xThemeConfigNavBgColor" :front-color="xThemeConfigNavFontColor"></navigation-bar>
	</page-meta>
	<!-- #endif -->

		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">拖拽排序 xDrag</x-text>
			<x-text color="#999999" >
				使用子组件窗口进行排序，组件内可以自由布局，最大的灵活度不影响你布局自己的外观。
				同时还支持设定col来定制为多列布局为宫格或者为列表模式。
			</x-text>
		</x-sheet>
		<x-sheet>
			<x-drag @change="onchange" v-if="list.length>0" :list="list">
				<x-drag-item
				 style="display: flex;flex-direction: column;"
				 v-for="(item,index) in list" 
				 :order="index" 
				 :disabled="index==1"
				 :key="index">
					<view 
					class="flex-1 mb-2 px-12 flex-row flex flex-row-center-start" 
					:style="{
						backgroundColor: bgColor,
						opacity:index==1?'0.6':'1'
					}"
					>
						<x-text>{{index==1?'我被固定在2':`长按${item.getNumber('id')}排序`}}</x-text>
					</view>
				</x-drag-item>
			</x-drag>
			<x-empty v-if="list.length==0" ></x-empty>
		</x-sheet>
		<x-sheet >
			<x-text font-size="18" class=" text-weight-b ">设置为2列形成宫格</x-text>
		</x-sheet>
		<x-sheet>
			<x-drag :col="2" :list="list2">
				<x-drag-item
				:disabled="index==2"
				 style="display: flex;flex-direction: column;"
				 v-for="(item,index) in list2" 
				 :order="index" 
				 :key="index">
					<view @click="onclick(index)" class="flex-1 mb-2 mx-2 px-12 flex-row flex flex-row-center-start" 
					:style="{
						backgroundColor: bgColor,
						opacity:index==2?'0.6':'1'
					}">
						<x-text>长按{{item.getNumber('id')}}排序</x-text>
					</view>
				</x-drag-item>
			</x-drag>
		</x-sheet>
		<x-sheet >
			<x-text font-size="18" class=" text-weight-b ">演示局部拖动,拖动时页面自动滚动</x-text>
		</x-sheet>
		<x-sheet>
			<x-drag :list="list4" @move="onmove" @start="ontouchstart" @end="ontouchend">
				<x-drag-item
				 style="display: flex;flex-direction: column;"
				 v-for="(item,index) in list4" 
				 :order="index" 
				 :key="index">
					<view class="flex-1 mb-2 px-12 flex-row flex flex-row-center-start" :style="{backgroundColor: bgColor}">
						<view 
						
						@longpress.stop="" 
						<!-- #ifdef WEB -->
						@mousedown.stop=""
						@mousemove.stop=""
						<!-- #endif -->
						@click.stop="onclick(index)"
						style="height:100%" 
						class="flex-1"
						>
							<x-text>{{index}}</x-text>
						</view>
						<view>
							<x-icon code="EF3E"></x-icon>
						</view>
					</view>
				</x-drag-item>
				
			</x-drag>
		</x-sheet>
		<x-sheet >
			<x-text font-size="18" class=" text-weight-b ">设置更多的列</x-text>
		</x-sheet>
		<x-sheet>
			<x-drag :col="4" :list="list3">
				<x-drag-item
				 style="display: flex;flex-direction: column;"
				 v-for="(item,index) in list3" 
				 :order="index" 
				 :key="index">
					<view class="flex-1 mb-2 mx-2 px-12 flex-row flex flex-row-center-start" :style="{backgroundColor: bgColor}">
						<x-text>长按排序</x-text>
					</view>
				</x-drag-item>
			</x-drag>
		</x-sheet>
		
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	import { xConfig } from "@/uni_modules/tmx-ui/config/xConfig.uts"
	export default {
		data() {
			return {
				list:[] as UTSJSONObject[],
				list2:[
					{id:1} as UTSJSONObject,
					{id:2} as UTSJSONObject,
					{id:3} as UTSJSONObject,
					{id:4} as UTSJSONObject,
					{id:5} as UTSJSONObject,
					{id:6} as UTSJSONObject
				] as UTSJSONObject[],
				list4:[
					{id:1} as UTSJSONObject,
					{id:2} as UTSJSONObject,
					{id:3} as UTSJSONObject,
					{id:4} as UTSJSONObject,
					{id:5} as UTSJSONObject,
					{id:6} as UTSJSONObject
				] as UTSJSONObject[],
				list3:[
					{id:1} as UTSJSONObject,
					{id:2} as UTSJSONObject,
					{id:3} as UTSJSONObject,
					{id:4} as UTSJSONObject,
					{id:5} as UTSJSONObject,
					{id:6} as UTSJSONObject,
					{id:7} as UTSJSONObject,
					{id:8} as UTSJSONObject,
					{id:9} as UTSJSONObject
				] as UTSJSONObject[],
				tid:10,
				nowPageScrollY:0,
				isMoveScroll:false,
				scrollDiff:25
			}
		},
		computed:{
			bgColor():string{
				return xConfig.dark=='dark'?'#333':'#e3e8f4'
			}
		},
		mounted() {
			let t = this;
			this.tid = setTimeout(function() {
				t.list = [
					{id:1} as UTSJSONObject,
					{id:2} as UTSJSONObject,
					{id:3} as UTSJSONObject,
					{id:4} as UTSJSONObject,
					{id:5} as UTSJSONObject,
					{id:6} as UTSJSONObject
				] as UTSJSONObject[]
			}, 500);
		},
		beforeUnmount() {
			clearTimeout(this.tid)
		},
		onPageScroll(opts) {
			if(this.isMoveScroll) return;
			this.nowPageScrollY = opts.scrollTop
			
		},
		methods: {
			ontouchstart(){
			
			},
			ontouchend(){
				this.isMoveScroll = false;
				
			},
			onmove(diff:number){
				let _this = this;
				this.isMoveScroll =true;
				_this.nowPageScrollY+=diff;
				uni.pageScrollTo({
					scrollTop:_this.nowPageScrollY
				})
			},
			onclick(index:number){
				uni.showToast({title:`点击了:${index}`,icon:'none'})
			},
			onchange(list:UTSJSONObject[]){
				console.warn("变动的数据：",list)
			}
		}
	}
</script>

<style>

</style>
