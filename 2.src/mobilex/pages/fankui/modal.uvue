<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
		<navigation-bar :background-color="xThemeConfigNavBgColor" :front-color="xThemeConfigNavFontColor"></navigation-bar>
	</page-meta>
	<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">对话框 Modal</x-text>
			<x-text color="#999999" >
				精致美观的对话框，可全局配置风格
			</x-text>
		</x-sheet>
		<x-sheet class="flex flex-row flex-row-center-center">
			<x-modal content-padding="24px 24px" @confirm="onconfirm" style="width:48%;margin-right:2%"  height="auto">
				<template #trigger><x-button :block="true">自动内容高</x-button></template>
				<x-input dark-bg-color="" placeholder="请输入密码"></x-input>
				<x-input dark-bg-color="" class="my-12" placeholder="请输入密码"></x-input>
				<view class="flex flex-row flex-row-center-between">
					<x-text class="text-size-m text-grey">同意协议</x-text>
					<x-switch ></x-switch>
				</view>
			</x-modal>

			<x-modal style="width:48%"  position="right" :show-close="true" :show-footer="false">
				<template #trigger><x-button  :block="true" color="warn">显示关闭</x-button></template>
				<x-text color="#999999" >
					精致美观的对话框，可全局配置风格
				</x-text>
			</x-modal>

		</x-sheet>

		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">默认高度，禁用遮罩关闭</x-text>
			<x-text color="#999999" class="mb-32">
				点击遮罩禁用关闭弹跳提示
			</x-text>
			
			<x-modal :overlayClick="false" confirmText="我已阅读" position="right">
				<template #trigger><x-button :block="true" color="error">默认高度,禁用遮罩</x-button></template>
				<x-text color="#999999" >
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
				</x-text>
			</x-modal>
		</x-sheet>

		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-16">嵌套</x-text>
			<x-modal height="auto">
				<template v-slot:trigger><x-button color="success" :block="true">打开嵌套</x-button></template>
				<x-modal height="auto">
					<template v-slot:trigger><x-button color="success" :block="true">打开嵌套1</x-button></template>
					<x-text class="text-size-m text-grey  line-8">当前弹层内容为自动高度</x-text>
					<x-modal height="auto">
						<template v-slot:trigger><x-button color="success" :block="true">打开嵌套2</x-button></template>
						<x-text class="text-size-m text-grey  line-8">当前弹层内容为自动高度</x-text>
					</x-modal>
				</x-modal>
			</x-modal>

		</x-sheet>
		
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-16">异步阻止关闭</x-text>
			<x-modal :beforeClose="beforeClose" height="auto">
				<template #trigger><x-button :block="true">打开异步关闭</x-button></template>
				<x-text class="text-size-m text-grey  line-8">只能关闭或者取消关闭或者点遮罩关闭，点确认按钮会异步阻止关闭。</x-text>
			</x-modal>
		
		</x-sheet>
		
		
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-16">底部插槽及变量控制显示</x-text>
			<x-button @click="show = true" :block="true">打开</x-button>
			<x-modal v-model:show="show" position="bottom" :show-footer="true">
				<x-text color="#999999" >
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
					精致美观的对话框，可全局配置风格
				</x-text>
				<template #footer>
					<x-button @click="show=false" :block="true" color="red" class="flex-1">定义底部插槽</x-button>
				</template>
			</x-modal>
		</x-sheet>

	<view style="height:50px"></view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				show2: false,
			};
		},
		methods:{
			onconfirm(){
				uni.navigateTo({
					url:"/pages/fankui/overlay"
				})
			},
			beforeClose():Promise<boolean>{
				return new Promise(res=>{
					setTimeout(function() {
						res(false)
					}, 2000);
				})
			}
		}
	}
</script>

<style lang="scss">

</style>