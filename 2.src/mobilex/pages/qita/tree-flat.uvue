<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<page-meta :page-style="`background-color:${xThemeConfigBgColor}`">
			<navigation-bar :background-color="xThemeConfigNavBgColor"
				:front-color="xThemeConfigNavFontColor"></navigation-bar>
		</page-meta>
		<!-- #endif -->
		<x-sheet>
			<x-text font-size="18" class=" text-weight-b mb-8">思维导图 xTreeFlat</x-text>
			<x-text color="#999999">
				这是一个横向布局的思维导图,可以用来展示结构或组织架构或流程,纯原生绘制,性能高
			</x-text>
		</x-sheet>
		<view class="flex-1">
			<!-- :list="nodes"  -->
			<x-tree-flat :opts="opts" ref="xmind" @init="oninit" height="100%"></x-tree-flat>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { XTreeFlatOpts,XTREEFLAT_NODES } from '@/uni_modules/tmx-ui/interface';
	import mockDatalist from "./mock.uts"
	const xmind = ref<XTreeFlatComponentPublicInstance | null>(null)
	const opts = { lineWidth:2 } as XTreeFlatOpts
	const oninit = () => {
		console.warn("导图引擎启动成功，可以设置数据了")
		xmind.value!.setData(mockDatalist)
	}
</script>

<style>
	/* #ifdef MP */
	page,
	body {
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	/* #endif */
</style>