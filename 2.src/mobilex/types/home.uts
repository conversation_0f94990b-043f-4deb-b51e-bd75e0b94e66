// Home 页面相关类型定义

// 定义新闻项类型
export type NewsItem = {
	title : string;
	date : string;
};

// 定义知识点项类型
export type KnowledgeItem = {
	id : string;
	text : string;
	children ?: KnowledgeItem[];
};

export type GridItem = {
	title : string;
	icon : string;
	color : string;
	badge : number;
};

export type TodayPlan = {
	title : string;
	completed : boolean;
	time : string;
};

export type StudyPlan = {
	title : string;
	description : string;
	progress : number;
};

export type News = {
	id : string;
	title : string;
	summary : string;
	content : string;
	source : string;
	publishTime : number;
	readCount : number;
	category : string;
	isHot : boolean;
	imageUrl : string;
	tags : string[];
	author : string;
	isCollected : boolean;
}

// 新闻评论
export type NewsComment = {
	id : string;
	newsId : string;
	userId : string;
	userName : string;
	userAvatar : string;
	content : string;
	createTime : number;
	likeCount : number;
	isLiked : boolean;
	replies : NewsCommentReply[];
}

// 新闻评论回复
export type NewsCommentReply = {
	id : string;
	userId : string;
	userName : string;
	userAvatar : string;
	content : string;
	createTime : number;
	replyToUserId : string;
	replyToUserName : string;
}

// 用户设置
export type UserSettings = {
	autoPlayNews : boolean;
	newsNotification : boolean;
	practiceReminder : boolean;
	reminderTime : string;
	fontSize : number;
	darkMode : boolean;
	vibration : boolean;
	sound : boolean;
}

// 下载内容
export type DownloadContent = {
	id : string;
	type : 'testpaper' | 'news' | 'video';
	name : string;
	size : number;
	downloadTime : number;
	filePath : string;
	isCompleted : boolean;
}