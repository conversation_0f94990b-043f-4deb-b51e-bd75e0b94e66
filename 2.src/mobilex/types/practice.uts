/**
 * 练题相关类型定义
 */

// 题目类型枚举
export type QuestionType = 'single' | 'multiple' | 'judge' | 'fill' | 'essay'

// 题目选项
export type QuestionOption = {
	id: string
	content: string
	isCorrect: boolean
}

// 题目信息
export type Question = {
	id: string
	type: QuestionType
	content: string
	options: QuestionOption[]
	correctAnswer: string[]
	analysis: string
	knowledgePoint: string
	difficulty: number
	isCollected: boolean
	userAnswer: string[] | null
	isAnswered: boolean
}

// 练题进度
export type Progress = {
	answeredCount: number
	totalCount: number
	correctCount: number
	percentage: number
}

// 试卷信息
export type TestPaper = {
	id: string
	name: string
	region: string
	year: number
	subject: string
	progress: Progress
	totalQuestions: number
	difficulty: number
	tags: string[]
	isDownloaded: boolean
	downloadProgress: number
	createTime: string
	updateTime: string
}

// 答题卡信息
export type AnswerCard = {
	questionId: string
	userAnswer: string[] | null
	isCorrect: boolean | null
	isCollected: boolean
	isAnswered: boolean
}

// 考试状态
export type ExamState = {
	currentQuestionIndex: number
	totalQuestions: number
	startTime: number
	elapsedTime: number
	isStudyMode: boolean
	answerCards: AnswerCard[]
	testPaper: TestPaper
	questions: Question[]
}

// 草稿信息
export type Draft = {
	questionId: string
	content: string
	createTime: number
	updateTime: number
}

// 设置选项
export type ExamSettings = {
	autoNext: boolean
	showAnalysis: boolean
	vibration: boolean
	sound: boolean
	fontSize: number
}

// 用户笔记
export type UserNote = {
	id: string
	questionId: string
	content: string
	subject: string
	knowledgePoint: string
	createTime: number
	updateTime: number
}

// 用户收藏
export type UserCollection = {
	id: string
	questionId: string
	question: Question
	subject: string
	knowledgePoint: string
	collectTime: number
}

// 练题记录
export type PracticeRecord = {
	id: string
	testPaperId: string
	testPaperName: string
	subject: string
	mode: 'sequence' | 'random' | 'exam' | 'wrong' | 'collection'
	startTime: number
	endTime: number
	totalQuestions: number
	answeredQuestions: number
	correctQuestions: number
	accuracy: number
	timeSpent: number
	isCompleted: boolean
}

// 练题统计
export type PracticeStats = {
	totalPracticeTime: number
	totalQuestions: number
	correctQuestions: number
	accuracy: number
	coverage: number
	continuousDays: number
	subjectStats: SubjectStats[]
}

// 科目统计
export type SubjectStats = {
	subject: string
	totalQuestions: number
	correctQuestions: number
	accuracy: number
	coverage: number
	practiceTime: number
}

// 用户评论
export type UserComment = {
	id: string
	questionId: string
	userId: string
	userName: string
	userAvatar: string
	content: string
	createTime: number
	likeCount: number
	isLiked: boolean
	replies: CommentReply[]
}

// 评论回复
export type CommentReply = {
	id: string
	userId: string
	userName: string
	userAvatar: string
	content: string
	createTime: number
	replyToUserId: string
	replyToUserName: string
}