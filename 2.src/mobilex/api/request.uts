import { RequestMethod, RequestOptions } from '../types/api.uts';

export function rq<T>(url: string, method: RequestMethod, data?: any, options?: RequestOptions): Promise<T | null> {
	let base = 'https://lightsoft-web-1304219145.cos.ap-shanghai.myqcloud.com';
	// #ifdef WEB
	if (process.env.NODE_ENV === 'development') {
		base = '/devapi/';
	}
	// #endif

	return new Promise((resolve, reject) => {
		const requestOptions: UniApp.RequestOptions = {
			url: base + url,
			method: method,
			success: (option: UniApp.RequestSuccessCallbackResult) => {
				resolve(option.data as T);
			},
			fail: (option: UniApp.GeneralCallbackResult) => {
				console.error(option);
				uni.showModal({
					title: "系统错误",
					content: `发生了系统错误,请重试`
				});
				reject(option);
			}
		};

		// 添加数据
		if (data != null) {
			requestOptions.data = data;
		}

		// 添加其他选项
		if (options != null) {
			if (options.headers != null) {
				requestOptions.header = options.headers;
			}
		}

		uni.request(requestOptions);
	});
}