import { rq } from './request.uts';
import {
  mockRegister,
  mockLogin,
  mockForgotPassword,
  mockChangePassword,
  mockUpdateProfile,
  mockGetNewsList,
  mockGetKnowledgeTree,
  mockGetTestPapers,
  mockGetPracticeRecords,
  mockGetPracticeStats,
  mockGetStudyPlans,
  mockCreateStudyPlan,
  mockGetPracticeDetail,
  mockGetUserInfo,
  mockUploadFile,
  mockGetSystemConfig
} from './mock.uts';
import type {
  User,
  LoginRequest,
  RegisterRequest,
  NewsItem,
  KnowledgePoint,
  PracticeRecord,
  PracticeStats,
  StudyPlan,
  PracticeDetail
} from '../types/mock.uts';
import type { TestPaper } from '../types/practice.uts';
import { 
  ApiResponse, 
  CommentQuestion, 
  WechatPayData, 
  WechatPayResponse, 
  OrderData, 
  ConfigResponse, 
  LoginData, 
  LoginResponse, 
  VideoItem, 
  UploadResponse,
  RequestMethod,
  RequestOptions
} from '../types/api.uts';
import type { MockApiResponse, MockLoginResponse } from '../types/mock.uts';

const baseUrl = 'https://app.lightsoft.tech/civilian';

// 开发模式标识，用于控制是否使用Mock数据
const isDevelopment = true; // 生产环境请设置为false

// 适配器函数：将生产环境API响应转换为MockApiResponse格式
const adaptApiResponse = <T>(data: T | null): MockApiResponse<T> => {
  if (data == null) {
    return {
      code: 500,
      message: '请求失败',
      data: data as T,
      timestamp: Date.now()
    };
  }
  return {
    code: 200,
    message: '请求成功',
    data: data,
    timestamp: Date.now()
  };
}
// 微信预支付
export const wechatPrePay = async (data: WechatPayData) : Promise<ApiResponse<WechatPayResponse>> => {
  return rq<WechatPayResponse>(`${baseUrl}/WxAppOpen/Prepay`, 'POST', data);
}

// 添加iOS订单
export const addIosOrder = async (data: OrderData) : Promise<ApiResponse<{ order_id: string }>> => {
  return rq<{ order_id: string }>(`${baseUrl}/orders/apple`, 'POST', data);
}

// 获取配置
export const getConfig = async () : Promise<ApiResponse<ConfigResponse>> => {
  return rq<ConfigResponse>(`${baseUrl}/configs`, 'GET');
}

// 微信登录
export const wechatLogin = async (data: LoginData) : Promise<ApiResponse<LoginResponse>> => {
  return rq<LoginResponse>(`${baseUrl}/accounts/wechat-login`, 'POST', data);
}

// 苹果登录
export const appleLogin = async (data: LoginData) : Promise<ApiResponse<LoginResponse>> => {
  return rq<LoginResponse>(`${baseUrl}/accounts/apple-login`, 'POST', data);
}

// 删除账号
export const deleteAccount = async () : Promise<ApiResponse<{ success: boolean }>> => {
  return rq<{ success: boolean }>(`${baseUrl}/accounts/delete`, 'DELETE');
}

// 检查是否激活
export const checkIsActived = async () : Promise<ApiResponse<{ is_actived: boolean }>> => {
  return rq<{ is_actived: boolean }>(`${baseUrl}/accounts/check-isactived`, 'GET');
}

// 获取视频
export const getVideos = async () : Promise<ApiResponse<VideoItem[]>> => {
  return rq<VideoItem[]>(`${baseUrl}/videos`, 'GET');
}

// ==================== Mock API 接口 ====================
// 注意：以下接口在开发模式下使用Mock数据，生产模式下需要替换为真实API

// 用户认证相关接口
export const register = async (data: RegisterRequest) : Promise<MockApiResponse<any>> => {
  if (isDevelopment) {
    return mockRegister(data);
  }
  // 生产环境下的真实API调用
  const result = await rq<any>(`${baseUrl}/auth/register`, 'POST', data);
  return adaptApiResponse(result);
}

export const login = async (data: LoginRequest) : Promise<MockApiResponse<any>> => {
  if (isDevelopment) {
    return mockLogin(data);
  }
  // 生产环境下的真实API调用
  const result = await rq<any>(`${baseUrl}/auth/login`, 'POST', data);
  return adaptApiResponse(result);
}

export const forgotPassword = async (email: string) : Promise<MockApiResponse<null>> => {
  if (isDevelopment) {
    return mockForgotPassword(email);
  }
  // 生产环境下的真实API调用
  const result = await rq<null>(`${baseUrl}/auth/forgot-password`, 'POST', { email });
  return adaptApiResponse(result);
}

export const changePassword = async (oldPassword: string, newPassword: string) : Promise<MockApiResponse<null>> => {
  if (isDevelopment) {
    return mockChangePassword(oldPassword, newPassword);
  }
  // 生产环境下的真实API调用
  const result = await rq<null>(`${baseUrl}/auth/change-password`, 'POST', { oldPassword, newPassword });
  return adaptApiResponse(result);
}

export const updateProfile = async (userId: string, data: Partial<User>) : Promise<MockApiResponse<User>> => {
  if (isDevelopment) {
    return mockUpdateProfile(userId, data);
  }
  // 生产环境下的真实API调用
  const result = await rq<User>(`${baseUrl}/users/${userId}`, 'PUT', data);
  return adaptApiResponse(result);
}

// 时政相关接口
export const getNewsList = async (page: number = 1, pageSize: number = 10, category?: string) : Promise<MockApiResponse<{ list: NewsItem[], total: number }>> => {
  if (isDevelopment) {
    return mockGetNewsList(page, pageSize, category);
  }
  // 生产环境下的真实API调用
  const params = { page, pageSize, category };
  const result = await rq<{ list: NewsItem[], total: number }>(`${baseUrl}/news`, 'GET', params);
  return adaptApiResponse(result);
}

// 知识点相关接口
export const getKnowledgeTree = async () : Promise<MockApiResponse<KnowledgePoint[]>> => {
  if (isDevelopment) {
    return mockGetKnowledgeTree();
  }
  // 生产环境下的真实API调用
  const result = await rq<KnowledgePoint[]>(`${baseUrl}/knowledge-points/tree`, 'GET');
  return adaptApiResponse(result);
}

// 试卷相关接口
export const getTestPapers = async (page: number = 1, pageSize: number = 10, filters?: { region?: string, year?: string, subject?: string }) : Promise<MockApiResponse<{ list: TestPaper[], total: number }>> => {
  if (isDevelopment) {
    return mockGetTestPapers(page, pageSize, filters);
  }
  // 生产环境下的真实API调用
  const params = { page, pageSize, ...filters };
  const result = await rq<{ list: TestPaper[], total: number }>(`${baseUrl}/test-papers`, 'GET', params);
  return adaptApiResponse(result);
}

// 练题记录相关接口
export const getPracticeRecords = async (userId: string, page: number = 1, pageSize: number = 10) : Promise<MockApiResponse<{ list: PracticeRecord[], total: number }>> => {
  if (isDevelopment) {
    return mockGetPracticeRecords(userId, page, pageSize);
  }
  // 生产环境下的真实API调用
  const params = { page, pageSize };
  const result = await rq<{ list: PracticeRecord[], total: number }>(`${baseUrl}/users/${userId}/practice-records`, 'GET', params);
  return adaptApiResponse(result);
}

// 练题统计相关接口
export const getPracticeStats = async (userId: string) : Promise<MockApiResponse<PracticeStats>> => {
  if (isDevelopment) {
    return mockGetPracticeStats(userId);
  }
  // 生产环境下的真实API调用
  const result = await rq<PracticeStats>(`${baseUrl}/users/${userId}/practice-stats`, 'GET');
  return adaptApiResponse(result);
}

// 学习计划相关接口
export const getStudyPlans = async (userId: string) : Promise<MockApiResponse<StudyPlan[]>> => {
  if (isDevelopment) {
    return mockGetStudyPlans(userId);
  }
  // 生产环境下的真实API调用
  const result = await rq<StudyPlan[]>(`${baseUrl}/users/${userId}/study-plans`, 'GET');
  return adaptApiResponse(result);
}

export const createStudyPlan = async (userId: string, planData: Omit<StudyPlan, 'id' | 'userId' | 'completedQuestions' | 'createdAt' | 'updatedAt'>) : Promise<MockApiResponse<StudyPlan>> => {
  if (isDevelopment) {
    return mockCreateStudyPlan(userId, planData);
  }
  // 生产环境下的真实API调用
  const result = await rq<StudyPlan>(`${baseUrl}/users/${userId}/study-plans`, 'POST', planData);
  return adaptApiResponse(result);
}

// 练习详情相关接口
export const getPracticeDetail = async (paperId?: string, knowledgePointId?: string) : Promise<MockApiResponse<PracticeDetail>> => {
  if (isDevelopment) {
    return mockGetPracticeDetail(paperId, knowledgePointId);
  }
  // 生产环境下的真实API调用
  const params = { paperId, knowledgePointId };
  const result = await rq<PracticeDetail>(`${baseUrl}/practice-detail`, 'GET', params);
  return adaptApiResponse(result);
}

// 其他业务接口
export const getUserInfo = async (userId: string) : Promise<MockApiResponse<User>> => {
  if (isDevelopment) {
    return mockGetUserInfo(userId);
  }
  // 生产环境下的真实API调用
  const result = await rq<User>(`${baseUrl}/users/${userId}`, 'GET');
  return adaptApiResponse(result);
}

export const uploadFile = async (file: any) : Promise<MockApiResponse<{ url: string }>> => {
  if (isDevelopment) {
    return mockUploadFile(file);
  }
  // 生产环境下的真实API调用
  const result = await rq<{ url: string }>(`${baseUrl}/upload`, 'POST', file);
  return adaptApiResponse(result);
}

export const getSystemConfig = async () : Promise<MockApiResponse<{ version: string, features: string[] }>> => {
  if (isDevelopment) {
    return mockGetSystemConfig();
  }
  // 生产环境下的真实API调用
  const result = await rq<{ version: string, features: string[] }>(`${baseUrl}/system/config`, 'GET');
  return adaptApiResponse(result);
}
