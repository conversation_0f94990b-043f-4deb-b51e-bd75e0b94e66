/**
 * Mock API 数据文件
 * 用于模拟后端接口返回的数据
 */

// 导入类型定义
import type {
	MockApiResponse,
	User,
	LoginRequest,
	RegisterRequest,
	MockLoginResponse,
	NewsItem,
	KnowledgePoint,
	PracticeRecord,
	PracticeStats,
	StudyPlan,
	PracticeDetail
} from '../types/mock.uts'
import type { TestPaper } from '../types/practice.uts'

// Mock 数据
// 模拟用户数据
const users: User[] = [
	{
		id: '1',
		username: 'testuser',
		nickname: '测试用户',
		avatar: 'https://via.placeholder.com/100',
		email: '<EMAIL>',
		phone: '13800138000',
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z'
	}
]

// 模拟时政数据
const newsItems: NewsItem[] = [
	{
		id: '1',
		title: '重要时政新闻标题',
		summary: '这是一条重要的时政新闻摘要...',
		content: '这是完整的新闻内容...',
		author: '新华社',
		publishTime: '2024-01-01T10:00:00Z',
		category: '国内新闻',
		tags: ['政治', '经济', '社会'],
		readCount: 1000,
		imageUrl: 'https://via.placeholder.com/300x200'
	}
]

// 模拟知识点数据
const knowledgePoints: KnowledgePoint[] = [
	{
		id: '1',
		name: '马克思主义基本原理',
		description: '马克思主义基本原理概论',
		parentId: null,
		level: 1,
		order: 1,
		children: [
			{
				id: '1-1',
				name: '马克思主义哲学',
				description: '马克思主义哲学基本原理',
				parentId: '1',
				level: 2,
				order: 1
			},
			{
				id: '1-2',
				name: '马克思主义政治经济学',
				description: '马克思主义政治经济学原理',
				parentId: '1',
				level: 2,
				order: 2
			}
		]
	}
]

// 模拟试卷数据
const testPapers: TestPaper[] = [
	{
		id: '1',
		name: '2024年公务员考试模拟卷一',
		region: '国家',
		year: 2024,
		subject: '行政职业能力测验',
		progress: {
			answeredCount: 0,
			totalCount: 100,
			correctCount: 0,
			percentage: 0
		},
		totalQuestions: 100,
		difficulty: 3,
		tags: ['公务员', '行测', '模拟卷'],
		isDownloaded: false,
		downloadProgress: 0,
		createTime: '2024-01-01T00:00:00Z',
		updateTime: '2024-01-01T00:00:00Z'
	}
]

// 工具方法：模拟网络延迟
function delay(ms: number): Promise<void> {
	return new Promise(resolve => {
		setTimeout(resolve, ms)
	})
}

// 用户认证相关接口
export async function mockRegister(data: RegisterRequest): Promise<MockApiResponse<MockLoginResponse>> {
	// 模拟注册逻辑
	await delay(1000)
	
	const newUser: User = {
		id: Date.now().toString(),
		username: data.username,
		nickname: data.nickname,
		avatar: 'https://via.placeholder.com/100',
		email: data.email,
		phone: data.phone,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString()
	}
	
	users.push(newUser)
	
	return {
		code: 200,
		message: '注册成功',
		data: {
			token: 'mock_token_' + Date.now(),
			user: newUser,
			expiresIn: 7200
		},
		timestamp: Date.now()
	}
}

export async function mockLogin(data: LoginRequest): Promise<MockApiResponse<MockLoginResponse>> {
	// 模拟登录逻辑
	await delay(1000)
	
	const user = users.find(u => u.username === data.username)
	if (!user) {
		return {
			code: 400,
			message: '用户名或密码错误',
			data: null,
			timestamp: Date.now()
		}
	}
	
	return {
		code: 200,
		message: '登录成功',
		data: {
			token: 'mock_token_' + Date.now(),
			user: user,
			expiresIn: 7200
		},
		timestamp: Date.now()
	}
}

export async function mockForgotPassword(email: string): Promise<MockApiResponse<null>> {
	// 模拟忘记密码逻辑
	await delay(1000)
	
	return {
		code: 200,
		message: '重置密码邮件已发送',
		data: null,
		timestamp: Date.now()
	}
}

export async function mockChangePassword(oldPassword: string, newPassword: string): Promise<MockApiResponse<null>> {
	// 模拟修改密码逻辑
	await delay(1000)
	
	return {
		code: 200,
		message: '密码修改成功',
		data: null,
		timestamp: Date.now()
	}
}

export async function mockUpdateProfile(userId: string, data: Partial<User>): Promise<MockApiResponse<User>> {
	// 模拟修改个人信息逻辑
	await delay(1000)
	
	const userIndex = users.findIndex(u => u.id === userId)
	if (userIndex === -1) {
		return {
			code: 404,
			message: '用户不存在',
			data: null,
			timestamp: Date.now()
		}
	}
	
	users[userIndex] = { ...users[userIndex], ...data, updatedAt: new Date().toISOString() }
	
	return {
		code: 200,
		message: '个人信息更新成功',
		data: users[userIndex],
		timestamp: Date.now()
	}
}

// 时政相关接口
export async function mockGetNewsList(page: number = 1, pageSize: number = 10, category?: string): Promise<MockApiResponse<{ list: NewsItem[], total: number }>> {
	// 模拟查询时政列表逻辑
	await delay(800)
	
	let filteredNews = newsItems
	if (category) {
		filteredNews = newsItems.filter(item => item.category === category)
	}
	
	const start = (page - 1) * pageSize
	const end = start + pageSize
	const list = filteredNews.slice(start, end)
	
	return {
		code: 200,
		message: '查询成功',
		data: {
			list: list,
			total: filteredNews.length
		},
		timestamp: Date.now()
	}
}

// 知识点相关接口
export async function mockGetKnowledgeTree(): Promise<MockApiResponse<KnowledgePoint[]>> {
	// 模拟查询知识点树状结构逻辑
	await delay(800)
	
	return {
		code: 200,
		message: '查询成功',
		data: knowledgePoints,
		timestamp: Date.now()
	}
}

// 试卷相关接口
export async function mockGetTestPapers(page: number = 1, pageSize: number = 10, filters?: { region?: string, year?: string, subject?: string }): Promise<MockApiResponse<{ list: TestPaper[], total: number }>> {
	// 模拟查询试卷列表逻辑
	await delay(800)
	
	let filteredPapers = testPapers
	if (filters) {
		if (filters.region) {
			filteredPapers = filteredPapers.filter(paper => paper.region === filters.region)
		}
		if (filters.year) {
			filteredPapers = filteredPapers.filter(paper => paper.year.toString() === filters.year)
		}
		if (filters.subject) {
			filteredPapers = filteredPapers.filter(paper => paper.subject === filters.subject)
		}
	}
	
	const start = (page - 1) * pageSize
	const end = start + pageSize
	const list = filteredPapers.slice(start, end)
	
	return {
		code: 200,
		message: '查询成功',
		data: {
			list: list,
			total: filteredPapers.length
		},
		timestamp: Date.now()
	}
}

// 练题记录相关接口
export async function mockGetPracticeRecords(userId: string, page: number = 1, pageSize: number = 10): Promise<MockApiResponse<{ list: PracticeRecord[], total: number }>> {
	// 模拟查询用户练题记录逻辑
	await delay(800)
	
	// 生成模拟数据
	const mockRecords: PracticeRecord[] = [
		{
			id: '1',
			userId: userId,
			paperId: '1',
			paperName: '2024年公务员考试模拟卷一',
			startTime: '2024-01-01T10:00:00Z',
			endTime: '2024-01-01T12:00:00Z',
			totalQuestions: 100,
			answeredQuestions: 95,
			correctQuestions: 80,
			score: 80,
			status: 'completed',
			createdAt: '2024-01-01T10:00:00Z'
		}
	]
	
	const start = (page - 1) * pageSize
	const end = start + pageSize
	const list = mockRecords.slice(start, end)
	
	return {
		code: 200,
		message: '查询成功',
		data: {
			list: list,
			total: mockRecords.length
		},
		timestamp: Date.now()
	}
}

// 练题统计相关接口
export async function mockGetPracticeStats(userId: string): Promise<MockApiResponse<PracticeStats>> {
	// 模拟查询用户练题统计信息逻辑
	await delay(800)
	
	const mockStats: PracticeStats = {
		totalPapers: 10,
		completedPapers: 8,
		totalQuestions: 1000,
		correctQuestions: 800,
		accuracy: 0.8,
		totalTime: 7200,
		averageScore: 80,
		weeklyStats: [
			{ date: '2024-01-01', questions: 50, correct: 40 },
			{ date: '2024-01-02', questions: 60, correct: 50 },
			{ date: '2024-01-03', questions: 45, correct: 38 }
		],
		subjectStats: [
			{ subject: '行政职业能力测验', accuracy: 0.85, questions: 500 },
			{ subject: '申论', accuracy: 0.75, questions: 300 }
		]
	}
	
	return {
		code: 200,
		message: '查询成功',
		data: mockStats,
		timestamp: Date.now()
	}
}

// 学习计划相关接口
export async function mockGetStudyPlans(userId: string): Promise<MockApiResponse<StudyPlan[]>> {
	// 模拟查询用户学习计划逻辑
	await delay(800)
	
	const mockPlans: StudyPlan[] = [
		{
			id: '1',
			userId: userId,
			name: '公务员考试备考计划',
			description: '为期3个月的公务员考试备考计划',
			startDate: '2024-01-01',
			endDate: '2024-03-31',
			targetQuestions: 3000,
			completedQuestions: 1500,
			status: 'active',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-15T00:00:00Z'
		}
	]
	
	return {
		code: 200,
		message: '查询成功',
		data: mockPlans,
		timestamp: Date.now()
	}
}

export async function mockCreateStudyPlan(userId: string, planData: Omit<StudyPlan, 'id' | 'userId' | 'completedQuestions' | 'createdAt' | 'updatedAt'>): Promise<MockApiResponse<StudyPlan>> {
	// 模拟创建学习计划逻辑
	await delay(1000)
	
	const newPlan: StudyPlan = {
		id: Date.now().toString(),
		userId: userId,
		...planData,
		completedQuestions: 0,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString()
	}
	
	return {
		code: 200,
		message: '学习计划创建成功',
		data: newPlan,
		timestamp: Date.now()
	}
}

// 练习详情相关接口
export async function mockGetPracticeDetail(paperId?: string, knowledgePointId?: string): Promise<MockApiResponse<PracticeDetail>> {
	// 模拟根据试卷id或知识点id获取练习详情逻辑
	await delay(1000)
	
	const mockDetail: PracticeDetail = {
		id: paperId || knowledgePointId || '1',
		name: paperId ? '试卷练习' : '知识点练习',
		description: paperId ? '基于试卷的练习' : '基于知识点的练习',
		questions: [
			{
				id: '1',
				type: 'single',
				content: '下列关于马克思主义的表述，正确的是？',
				options: [
					{ id: 'A', content: '马克思主义是科学的世界观和方法论', isCorrect: true },
					{ id: 'B', content: '马克思主义是绝对真理', isCorrect: false },
					{ id: 'C', content: '马克思主义是永恒不变的', isCorrect: false },
					{ id: 'D', content: '马克思主义只适用于西方国家', isCorrect: false }
				],
				correctAnswer: ['A'],
				analysis: '马克思主义是科学的世界观和方法论，具有科学性、革命性、实践性、人民性和发展性的特征。',
				knowledgePoint: '马克思主义基本原理',
				difficulty: 2,
				isCollected: false,
				userAnswer: null,
				isAnswered: false
			},
			{
				id: '2',
				type: 'multiple',
				content: '马克思主义的基本组成部分包括（）',
				options: [
					{ id: 'A', content: '马克思主义哲学', isCorrect: true },
					{ id: 'B', content: '马克思主义政治经济学', isCorrect: true },
					{ id: 'C', content: '科学社会主义', isCorrect: true },
					{ id: 'D', content: '马克思主义文学', isCorrect: false }
				],
				correctAnswer: ['A', 'B', 'C'],
				analysis: '马克思主义由马克思主义哲学、马克思主义政治经济学和科学社会主义三个基本组成部分构成。',
				knowledgePoint: '马克思主义基本原理',
				difficulty: 3,
				isCollected: false,
				userAnswer: null,
				isAnswered: false
			}
		],
		totalQuestions: 2,
		totalScore: 100,
		duration: 30
	}
	
	return {
		code: 200,
		message: '查询成功',
		data: mockDetail,
		timestamp: Date.now()
	}
}

// 其他业务接口
export async function mockGetUserInfo(userId: string): Promise<MockApiResponse<User>> {
	// 模拟获取用户信息逻辑
	await delay(500)
	
	const user = users.find(u => u.id === userId)
	if (!user) {
		return {
			code: 404,
			message: '用户不存在',
			data: null,
			timestamp: Date.now()
		}
	}
	
	return {
		code: 200,
		message: '查询成功',
		data: user,
		timestamp: Date.now()
	}
}

export async function mockUploadFile(file: any): Promise<MockApiResponse<{ url: string }>> {
	// 模拟文件上传逻辑
	await delay(2000)
	
	return {
		code: 200,
		message: '文件上传成功',
		data: {
			url: 'https://via.placeholder.com/300x200?text=Uploaded+File'
		},
		timestamp: Date.now()
	}
}

export async function mockGetSystemConfig(): Promise<MockApiResponse<{ version: string, features: string[] }>> {
	// 模拟获取系统配置逻辑
	await delay(300)
	
	return {
		code: 200,
		message: '查询成功',
		data: {
			version: '1.0.0',
			features: ['practice', 'news', 'study-plan', 'statistics']
		},
		timestamp: Date.now()
	}
}

// 导出所有 Mock 方法（保持向后兼容）
export {
	mockRegister as register,
	mockLogin as login,
	mockForgotPassword as forgotPassword,
	mockChangePassword as changePassword,
	mockUpdateProfile as updateProfile,
	mockGetNewsList as getNewsList,
	mockGetKnowledgeTree as getKnowledgeTree,
	mockGetTestPapers as getTestPapers,
	mockGetPracticeRecords as getPracticeRecords,
	mockGetPracticeStats as getPracticeStats,
	mockGetStudyPlans as getStudyPlans,
	mockCreateStudyPlan as createStudyPlan,
	mockGetPracticeDetail as getPracticeDetail,
	mockGetUserInfo as getUserInfo,
	mockUploadFile as uploadFile,
	mockGetSystemConfig as getSystemConfig
}